# FAQ

## cargo test 报错 undefined symbol: ffi_type_void, version LIBFFI_BASE_7.0 怎么办？

最快的方法
```
mv {$CONDA_PREFIX}/lib/libffi.so.7 {$CONDA_PREFIX}/lib/libffi.so.7.bak
```

也可以不用 conda 环境，切换到系统自身的 python 环境 （或者别的 python 版本管理工具） 也能解决问题

## 找不到链接库： error while loading shared libraries: libpython3.10.so.1.0: cannot open shared object file: No such file or directory 怎么办？

用写好的cargo make

```
cargo make nextest ....
```

实际上是手动指定了链接库搜索路径： `LD_LIBRARY_PATH="${CONDA_PREFIX}/lib"`