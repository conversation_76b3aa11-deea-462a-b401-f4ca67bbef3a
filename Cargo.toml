[workspace]
members = ["py-sigrun", "crates/*"]
resolver = "3"
default-members = ["crates/*"]
package.version = "0.13.0-a4"

[workspace.dependencies]
pypolars = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32", package = "py-polars" }
polars-python = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-core = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-plan = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-arrow = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-mem-engine = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-expr = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-utils = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-time = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
virgo = { git = "https://gitlab.sci-inv.cn/rustlib/virgo-rs.git" }
# virgo = { path = "../virgo-rs/virgo" }
sigrun-loader = { git = "https://gitlab.sci-inv.cn/rustlib/sigrun-loader.git" }
# sigrun-loader = { path = "../sigrun-loader" }
pyo3-utils = { git = "https://gitlab.sci-inv.cn/rustlib/pyo3-utils.git" }
datetime-iterator = { git = "https://gitlab.sci-inv.cn/rustlib/datetime-iterator.git" }
dyconfig = { git = "https://gitlab.sci-inv.cn/rustlib/dyconfig-rs.git" }
batch-parquet-reader = { git = "https://gitlab.sci-inv.cn/rustlib/batch-parquet-reader.git" }


pyo3 = "0.25"

# [patch.'https://gitlab.sci-inv.cn/rustlib/batch-parquet-reader.git']
# batch-parquet-reader = { path = "../batch-parquet-reader" }

[patch.crates-io]
polars = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-python = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-plan = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-arrow = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-compute = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-utils = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-error = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-row = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-core = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-io = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-lazy = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-parquet = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-time = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-expr = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-ops = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }
polars-mem-engine = { git = "https://gitlab.sci-inv.cn/rustlib/polars.git", branch = "1.32" }

polars-arrow-format = { git = "https://gitlab.sci-inv.cn/rustlib/arrow-format.git" }

# [patch.'https://gitlab.sci-inv.cn/rustlib/polars.git']
# polars = { path = "../polars/crates/polars" }
# polars-plan = { path = "../polars/crates/polars-plan" }
# polars-arrow = { path = "../polars/crates/polars-arrow" }
# polars-compute = { path = "../polars/crates/polars-compute" }
# polars-utils = { path = "../polars/crates/polars-utils" }
# polars-error = { path = "../polars/crates/polars-error" }
# polars-row = { path = "../polars/crates/polars-row" }
# polars-core = { path = "../polars/crates/polars-core" }
# polars-io = { path = "../polars/crates/polars-io" }
# polars-lazy = { path = "../polars/crates/polars-lazy" }
# polars-parquet = { path = "../polars/crates/polars-parquet" }
# polars-time = { path = "../polars/crates/polars-time" }
# polars-expr = { path = "../polars/crates/polars-expr" }
# polars-ops = { path = "../polars/crates/polars-ops" }
# polars-ffi = { path = "../polars/crates/polars-ffi" }
# polars-mem-engine = { path = "../polars/crates/polars-mem-engine" }


[profile.release]
codegen-units = 1
lto = true

[profile.release-thin]
inherits = "release"
codegen-units = 16
lto = "thin"
debug = true


[profile.cli]
inherits = "release"
panic = "abort"
