import sigrun as sr
from sigrun import polars as pl
from sigrun.workflow import factor, parametrize


def test_run_all():
    @factor
    @parametrize("symbol", ["A", "B"])
    def inner1(date, time, symbol):
        return pl.DataFrame({"inner1": [symbol]}).lazy()

    @factor
    @parametrize("symbol", ["A", "B"])
    def inner2(date, time, symbol):
        return pl.DataFrame({"inner2": [symbol]}).lazy()

    df = sr.run_all(
        [inner1, inner2],
        start_date="2025-03-25",
        end_date="2025-03-25",
        times=["09:00:00"],
    )
    assert len(df) == 2
    assert df[0].shape == (2, 3)
    assert df[0].columns == ["inner1", "date", "time"]
    assert df[1].shape == (2, 3)
    assert df[1].columns == ["inner2", "date", "time"]
