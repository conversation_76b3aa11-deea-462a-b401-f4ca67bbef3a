import sigrun as sr
import datetime
import pandas as pd


def test_to_timedelta():
    assert sr.to_timedelta("09:31:00") == pd.to_timedelta("09:31:00")
    assert sr.to_timedelta(datetime.time(9, 31, 0)) == pd.to_timedelta("09:31:00")

    assert sr.to_timedelta(1000000000) == pd.to_timedelta(1000000000)


def test_time_before():
    assert sr.time_before("13:00:00", minutes=30) == datetime.time(11, 0, 0)
