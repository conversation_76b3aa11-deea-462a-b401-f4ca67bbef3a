import datetime
import os
from unittest import skipIf

import sigrun as sr
from sigrun import polars as pl
from sigrun import virgo
from sigrun.workflow import factor


@skipIf(os.environ.get("PYTEST_REALTIME") is None, "realtime disabled")
def test_realtime_bar():
    today = datetime.date.today()

    @factor
    def generate_factor(date, time):
        return virgo.stock.bars(
            "sse",
            "1m",
            date,
            from_time="09:31:00",
            to_time="09:35:00",
            force_columns=["symbol", "time"],
        )

    df = sr.run(
        generate_factor,
        start_date=today,
        end_date=today,
        times=["09:31:00"],
    )

    assert df.columns == ["symbol", "time", "date"]
    assert len(df) > 0

    print(df)


def test_run():
    @factor
    def generate_factor(date, time):
        df = virgo.stock.orders(
            "all",
            date,
            from_time="10:00:00",
            to_time="10:30:00",
        )

        df1 = df.filter(pl.col("order_type") == "D")
        df2 = df.filter((pl.col("order_type") != "D") & (pl.col("order_type") != "U"))

        ret = pl.concat([df1, df2], how="vertical").select(["symbol", "time", "date"])

        return ret

    df = sr.run(
        generate_factor,
        start_date=datetime.date(2025, 4, 14),
        end_date=datetime.date(2025, 4, 14),
        times=["09:31:00"],
    )

    assert df.columns == ["symbol", "time", "date"]
    assert len(df) > 0

    print(df)


def test_collect_in_run():
    os.environ["SIGRUN_IO_ENGINE"] = "false"

    @factor
    def generate_factor(date, time):
        df = virgo.stock.orders(
            "all",
            date,
            from_time="10:00:00",
            to_time="10:30:00",
        ).cache()

        df1 = df.filter(pl.col("order_type") == "D")
        df2 = df.filter((pl.col("order_type") != "D") & (pl.col("order_type") != "U"))

        ret = pl.concat([df1, df2], how="vertical").select(["symbol", "time", "date"])

        assert len(ret.collect()) > 0

        return ret

    df = sr.run(
        generate_factor,
        start_date=datetime.date(2025, 4, 14),
        end_date=datetime.date(2025, 4, 14),
        times=["09:31:00"],
    )

    assert df.columns == ["symbol", "time", "date"]
    assert len(df) > 0

    print(df)


def test_cache_scan():
    @factor
    def generate_factor(date, time):
        return virgo.table.scan(
            "market.etf.bar_1m", start_date=date, end_date=date
        ).cache()

    df = sr.run(
        generate_factor,
        start_date=datetime.date(2025, 4, 14),
        end_date=datetime.date(2025, 4, 14),
        times=["09:31:00"],
    )

    assert len(df) > 0


def test_cache_filter_scan():
    @factor
    def generate_factor(date, time):
        return (
            virgo.table.scan("market.etf.bar_1m", start_date=date, end_date=date)
            .filter(pl.col("pre_close") > 10)
            .cache()
        )

    df = sr.run(
        generate_factor,
        start_date=datetime.date(2025, 4, 14),
        end_date=datetime.date(2025, 4, 14),
        times=["09:31:00"],
    )

    assert len(df) > 0


def test_multi_cache():
    @factor
    def generate_factor(date, time):
        lf1 = (
            virgo.table.scan("market.etf.bar_1m", start_date=date, end_date=date)
            .filter(pl.col("pre_close") > 10)
            .cache()
        )

        lf2 = (
            virgo.table.scan("market.etf.bar_1m", start_date=date, end_date=date)
            .filter(pl.col("pre_close") > 10)
            .cache()
        )

        return pl.concat([lf1, lf2], how="vertical")

    df = sr.run(
        generate_factor,
        start_date=datetime.date(2025, 4, 14),
        end_date=datetime.date(2025, 4, 14),
        times=["09:31:00"],
    )

    assert len(df) > 0


def test_cache_in_cache():
    @factor
    def generate_factor(date, time):
        return (
            virgo.table.scan("market.etf.bar_1m", start_date=date, end_date=date)
            .cache()
            .filter(pl.col("pre_close") > 10)
            .cache()
        )

    df = sr.run(
        generate_factor,
        start_date=datetime.date(2025, 4, 14),
        end_date=datetime.date(2025, 4, 14),
        times=["09:31:00"],
    )

    assert len(df) > 0


def test_filter_not_match():
    # 命中 cache 后，未正确处理 branch idx 可能导致 filter length not match
    @factor
    def generate_factor(date, time):
        lf1 = pl.LazyFrame({"x": [True] * 2}).cache()
        lf2 = pl.LazyFrame({"x": [True] * 3})

        a = lf1
        b = lf1.filter(pl.col("x").not_().over(1))
        c = lf2.filter(pl.col("x").not_().over(1))

        ab = a.join(b, on="x")
        bc = b.join(c, on="x")
        ac = a.join(c, on="x")

        return pl.concat([ab, bc, ac])

    df = sr.run(
        generate_factor,
        start_date=datetime.date(2025, 4, 14),
        end_date=datetime.date(2025, 4, 14),
        times=["09:31:00"],
    )

    assert len(df) == 0


def test_concat_and_select():
    # https://github.com/pola-rs/polars/pull/20406 导致 concat 后无法正常投影下推，要求两个Schema严格一致
    @factor
    def generate_factor(date, time):
        lf1 = pl.LazyFrame({"x": [1], "y": [2]})
        lf2 = pl.LazyFrame({"y": [1]})

        return pl.concat([lf1, lf2]).select("y")

    df = sr.run(
        generate_factor,
        start_date=datetime.date(2025, 4, 14),
        end_date=datetime.date(2025, 4, 14),
        times=["09:31:00"],
    )

    assert len(df) == 2
