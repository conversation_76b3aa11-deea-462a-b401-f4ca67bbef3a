import os

from sigrun import polars as pl
from sigrun import virgo


def test_scan_cse():
    os.environ["SIGRUN_IO_ENGINE"] = "false"

    def build() -> pl.LazyFrame:
        lf1 = (
            virgo.table.scan("market.insightkline", date="2018-01-02")
            .filter(pl.col("time") >= "9:00:00")
            .with_columns(open=pl.col("open") + 1)
        )

        lf2 = lf1.with_columns(close=pl.col("close") + 1)

        return pl.concat([lf1, lf2], how="vertical").select(
            "symbol", "time", "open", "close"
        )

    plan = build().explain()

    assert plan.count("CACHE[id: 0, cache_hits: 1]") == 2
    assert plan.count("CACHE") == 2

    print(plan)
