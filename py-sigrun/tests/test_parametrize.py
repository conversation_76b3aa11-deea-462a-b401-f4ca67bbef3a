import sigrun as sr
from sigrun import Marker, virgo
from sigrun import polars as pl
from sigrun.workflow import factor, parametrize


def test_parametrize():
    @factor
    @parametrize("symbol", ["A", "B"])
    def inner(date, time, symbol):
        return pl.DataFrame({"symbol": [symbol], "date": [date], "time": [time]}).lazy()

    df = sr.run(
        inner, start_date="2025-03-25", end_date="2025-03-25", times=["09:00:00"]
    )
    assert df.shape == (2, 3)

    df = sr.run(
        inner,
        start_date="2025-03-25",
        end_date="2025-03-25",
        times=["09:00:00"],
        markers=[Marker("symbol", ["A", "B", "C"])],
    )
    assert df.shape == (3, 3)


def test_parametrize_high_freq():
    @factor
    @parametrize("symbol", ["A", "B"])
    def inner(date, time, symbol):
        return virgo.stock.bars("all", "1m", date).select(pl.col("symbol"))

    df = sr.run(
        inner, start_date="2024-03-11", end_date="2024-03-11", times=["09:00:00"]
    )
    assert df.shape == (1231028 * 2, 3)

    df = sr.run(
        inner,
        start_date="2024-03-11",
        end_date="2024-03-11",
        times=["09:00:00"],
        markers=[Marker("symbol", ["A", "B", "C"])],
    )
    assert df.shape == (1231028 * 3, 3)


def test_parametrize_virgo_table():
    @factor
    @parametrize("symbol", ["A", "B"])
    def inner(date, time, symbol):
        return pl.DataFrame({"symbol": [symbol], "date": [date], "time": [time]}).lazy()

    df = sr.run(
        inner, start_date="2025-03-25", end_date="2025-03-25", times=["09:00:00"]
    )
    assert df.shape == (2, 3)

    df = sr.run(
        inner,
        start_date="2025-03-25",
        end_date="2025-03-25",
        times=["09:00:00"],
        markers=[Marker("symbol", ["A", "B", "C"])],
    )
    assert df.shape == (3, 3)
