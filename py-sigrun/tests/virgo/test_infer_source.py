import json
import os

import pytest
import sigrun as sr
from sigrun import virgo
from sigrun.workflow import factor


@pytest.mark.parametrize("enable_io_engine", ["true", "false"])
def test_infer_virgo_table_source(enable_io_engine):
    os.environ["SIGRUN_IO_ENGINE"] = enable_io_engine

    @factor
    def inner(date, time):
        start_date = virgo.market.trading_day(date=date, span=-50)
        lf1 = virgo.table.scan(
            "market.etf.bar_1m", start_date=start_date, end_date=date
        )
        lf2 = virgo.table.scan(
            "market.stock_limit_price_v2", start_date=start_date, end_date=date
        )
        return lf1.join(lf2, on="symbol", how="left")

    sources = sr.infer_virgo_table_sources(inner)
    assert len(sources) == 2

    assert sources[0][0] == "market.stock_limit_price_v2"
    opt = json.loads(sources[0][1])
    assert opt["start_date"] == opt["end_date"]

    assert sources[1][0] == "market.etf.bar_1m"
    opt = json.loads(sources[1][1])
    assert opt["start_date"] == opt["end_date"]


def test_infer_high_freq_source():
    os.environ["SIGRUN_IO_ENGINE"] = "true"

    @factor
    def inner(date, time):
        lf1 = virgo.stock.bars(symbols="all", from_date=date, to_date=date, freq="1m")
        lf2 = virgo.stock.matches(symbols="all", from_date=date, to_date=date)
        return lf1.join(lf2, on="symbol", how="left")

    schema = sr.infer_virgo_table_sources(
        inner, date="2024-11-11", filter_high_freq=False
    )
    assert len(schema) == 2

    assert schema[0][0] == "stock.match.2024-11-11.2024-11-11.ALL"
    assert schema[0][1] == ""

    assert schema[1][0] == "stock.bar_1m.2024-11-11.2024-11-11.ALL"
    assert schema[1][1] == ""
