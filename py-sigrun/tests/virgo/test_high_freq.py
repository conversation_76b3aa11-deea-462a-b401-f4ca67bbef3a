import datetime
import os
from unittest import TestCase, skipIf

from sigrun import virgo


class TestHighFreq(TestCase):
    def test_simple_read(self):
        df = virgo.stock.bars("all", "1m", "2024-03-11").collect()
        self.assertEqual(df.shape, (1231028, 12))

    def test_read_interval(self):
        df = virgo.stock.bars("all", "1m", "2024-03-11", "2024-03-12").collect()
        self.assertEqual(df.shape, (2462056, 12))

    def test_read_time(self):
        df = virgo.stock.bars(
            "all", "1m", "2024-03-11", from_time="10:05:00", to_time="10:41:00"
        ).collect()
        self.assertEqual(df.shape, (188996, 12))

    def test_read_columns(self):
        df = (
            virgo.stock.bars("all", "1m", "2024-03-11")
            .select("symbol", "time", "close", "volume")
            .collect()
        )
        self.assertEqual(df.shape, (1231028, 4))

    def test_read_exchange(self):
        df = virgo.stock.bars("sse", "1m", "2024-03-11").collect()
        self.assertEqual(df.shape, (546106, 12))

        df = virgo.stock.bars("SSE", "1m", "2024-03-11").collect()
        self.assertEqual(df.shape, (546106, 12))

        df = virgo.stock.bars("sze", "1m", "2024-03-11").collect()
        self.assertEqual(df.shape, (684922, 12))

    def test_read_stock(self):
        df = virgo.stock.bars("000001.SZE", "1m", "2024-03-11").collect()
        self.assertEqual(df.shape, (241, 12))

        df = virgo.stock.bars(
            ["000001.SZE", "000002.SZE", "600000.SSE"], "1m", "2024-03-11"
        ).collect()
        self.assertEqual(df.shape, (241 * 3, 12))

    def test_stock_match(self):
        df = virgo.stock.matches("sse", "2011-01-04").select("symbol", "date").collect()
        self.assertEqual(df.shape, (6626539, 2))

    def test_stock_order(self):
        df = virgo.stock.orders("all", "2011-01-04").select("symbol", "date").collect()
        self.assertEqual(df.shape, (10627595, 2))

    def test_stock_snapshots(self):
        df = (
            virgo.stock.snapshots("sse", "2011-01-04")
            .select("symbol", "date")
            .collect()
        )
        self.assertEqual(df.shape, (2915772, 2))

    def test_index_snapshots(self):
        df = (
            virgo.index.snapshots("sse", "2015-01-12")
            .select("symbol", "date")
            .collect()
        )
        self.assertEqual(df.shape, (675001, 2))

    def test_index_bars(self):
        df = (
            virgo.index.bars("sse", "5m", "2015-01-12")
            .select("symbol", "date")
            .collect()
        )
        self.assertEqual(df.shape, (12103, 2))

    def test_future_snapshots(self):
        df = (
            virgo.future.snapshots("all", "2018-01-04")
            .select("symbol", "date")
            .collect()
        )
        self.assertEqual(df.shape, (119274, 2))

    def test_future_bars(self):
        df = (
            virgo.future.bars("all", "1m", "2018-01-04")
            .select("symbol", "date")
            .collect()
        )
        self.assertEqual(df.shape, (2892, 2))

    @skipIf(os.environ.get("PYTEST_REALTIME") is None, "realtime disabled")
    def test_read_realtime(self):
        today = datetime.date.today().strftime("%Y-%m-%d")
        df = virgo.stock.matches(
            "sse", today, from_time="09:31:00", to_time="09:35:00"
        ).collect()
        t_min = df["time"].min()
        t_max = df["time"].max()
        diff1 = (t_min - datetime.timedelta(hours=9, minutes=31)).total_seconds()
        diff2 = (datetime.timedelta(hours=9, minutes=35) - t_max).total_seconds()
        self.assertLessEqual(diff1, 0.1)
        self.assertLessEqual(diff2, 0.1)

    @skipIf(os.environ.get("PYTEST_REALTIME") is None, "realtime disabled")
    def test_read_realtime_bar(self):
        today = datetime.date.today().strftime("%Y-%m-%d")
        df = virgo.stock.bars(
            "sse", "1m", today, from_time="09:31:00", to_time="09:35:00"
        ).collect()
        df_g = df.group_by("time").len()
        self.assertEqual(df_g["len"].max(), df_g["len"].min())
