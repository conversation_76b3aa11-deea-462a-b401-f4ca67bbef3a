from datetime import date as d

import pytest
from sigrun import virgo


@pytest.mark.parametrize("start", ["2021-01-01", d(2021, 1, 1)])
@pytest.mark.parametrize(
    "end, length",
    [
        ("2022-01-01", 243),
        (d(2022, 1, 1), 243),
        ("2022-01-05", 245),
        (d(2022, 1, 5), 245),
    ],
)
def test_trading_days(start, end, length):
    dates = virgo.market.trading_days(start, end)
    assert len(dates) == length


@pytest.mark.parametrize("date", ["2021-01-01", d(2021, 1, 1)])
def test_trading_day(date):
    assert isinstance(virgo.market.trading_day(date=date), type(None))
    assert isinstance(virgo.market.trading_day(date=date, span=0), type(None))

    assert virgo.market.trading_day(date=date, span=1) == d(2021, 1, 4)
    assert virgo.market.trading_day(date=date, span=-1) == d(2020, 12, 31)
    assert virgo.market.trading_day(date=date, span=15) == d(2021, 1, 22)
    assert virgo.market.trading_day(date=date, span=-15) == d(2020, 12, 11)


@pytest.mark.parametrize("date", ["2021-01-21", d(2021, 1, 21)])
def test_trading_day_1(date):
    assert virgo.market.trading_day(date=date) == d(2021, 1, 21)
    assert virgo.market.trading_day(date=date, span=0) == d(2021, 1, 21)
    assert virgo.market.trading_day(date=date, span=1) == d(2021, 1, 22)
    assert virgo.market.trading_day(date=date, span=-1) == d(2021, 1, 20)
    assert virgo.market.trading_day(date=date, span=15) == d(2021, 2, 18)
    assert virgo.market.trading_day(date=date, span=-15) == d(2020, 12, 30)


def test_is_trading_day():
    assert virgo.market.is_trading_day("2021-01-04")
    assert not virgo.market.is_trading_day("2021-01-01")
    assert virgo.market.is_trading_day("2021-01-21")
    assert not virgo.market.is_trading_day("2021-01-24")


def test_invalid_date():
    with pytest.raises(ValueError):
        virgo.market.trading_days("2021-01-01", "2020-0101")

    with pytest.raises(ValueError):
        virgo.market.trading_day(1, "2020-0101")

    with pytest.raises(ValueError):
        virgo.market.is_trading_day("2020-0101")


def test_default_args():
    assert isinstance(virgo.market.is_trading_day(), bool)
    assert isinstance(virgo.market.trading_day(), (d, type(None)))
