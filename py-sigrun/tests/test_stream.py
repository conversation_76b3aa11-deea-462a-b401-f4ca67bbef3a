import datetime

import sigrun as sr
from sigrun import polars as pl
from sigrun import virgo
from sigrun.workflow import parametrize, stream

import os

os.environ["VIRGO_SHARED_DATA_PATH"] = "/root/python/my_factor/data/stream"


def test_run():
    @stream("test")
    def generate_factor():
        window = virgo.stream.tumbling(width=datetime.timedelta(minutes=30))

        df = virgo.stream.order_level().windows(window)

        df1 = df.filter(pl.col("order_type") == 0).select(
            ["order_type", "symbol", "time", "date"]
        )
        df2 = df.filter(pl.col("price_level") == 1)

        ret = pl.concat([df1, df2], how="vertical").select(["symbol"])

        return ret

    df = sr.run_stream(
        generate_factor,
        start_date=datetime.date(2024, 12, 31),
        end_date=datetime.date(2024, 12, 31),
        end_time=datetime.time(11, 0),
    )

    print(df)

    assert df.columns == ["symbol", "date", "time"]
    assert len(df) > 0


def test_parametrize():
    @stream("test")
    @parametrize("symbol", ["A", "B"])
    def generate_factor(symbol):
        window = virgo.stream.tumbling(width=datetime.timedelta(minutes=30))

        df = virgo.stream.order_level().windows(window)

        df1 = df.filter(pl.col("order_type") == 0).select(
            ["order_type", "symbol", "time", "date"]
        )
        df2 = df.filter(pl.col("price_level") == 1)

        ret = (
            pl.concat([df1, df2], how="vertical")
            .select(["symbol"])
            .with_columns(t=pl.lit(symbol))
        )

        return ret

    df = sr.run_stream(
        generate_factor,
        start_date=datetime.date(2024, 12, 31),
        end_date=datetime.date(2024, 12, 31),
        end_time=datetime.time(11, 0),
    )

    print(df)

    assert df.columns == ["symbol", "t", "date", "time"]
    assert len(df) > 0


def test_high_freq():
    @stream
    def generate_factor():
        window = virgo.stream.tumbling(width=datetime.timedelta(minutes=30))

        df = virgo.stream.stock.orders().windows(window)

        df1 = df.filter(pl.col("order_type") == "D")
        df2 = df.filter((pl.col("order_type") != "D") & (pl.col("order_type") != "U"))

        ret = pl.concat([df1, df2], how="vertical").select(["symbol", "time", "date"])

        return ret

    df = sr.run_stream(
        generate_factor,
        start_date=datetime.date(2024, 12, 31),
        end_date=datetime.date(2024, 12, 31),
        end_time=datetime.time(11, 0),
    )

    print(df)

    assert df.columns == ["symbol", "time", "date"]
    assert len(df) > 0
