use std::time::Duration;

use chrono::NaiveTime;
use pyo3::exceptions::PyValueError;
use pyo3::pybacked::PyBackedStr;
use pyo3::pyfunction;
use pyo3::{FromPyObject, PyResult};
use pyo3_utils::StrOrT;

#[derive(FromPyObject)]
pub enum Input {
    Str(PyBackedStr),
    Time(NaiveTime),
    Number(u64),
}

impl Input {
    fn into_timedelta(self) -> PyResult<Duration> {
        const BASE: NaiveTime = NaiveTime::from_hms_opt(0, 0, 0).unwrap();

        match self {
            Self::Str(s) => NaiveTime::parse_from_str(&s, "%H:%M:%S")
                .map_err(|e| PyValueError::new_err(e.to_string()))
                .map(|t| (t - BASE).to_std().unwrap()),
            Self::Time(t) => Ok((t - BASE).to_std().unwrap()),
            Self::Number(n) => Ok(Duration::from_nanos(n)),
        }
    }
}

#[pyfunction]
#[pyo3(signature = (input))]
pub fn py_to_timedelta(input: Input) -> PyResult<Duration> {
    input.into_timedelta()
}

const RANGE: TimeRange = TimeRange::new(
    NaiveTime::from_hms_opt(11, 30, 0).unwrap(),
    NaiveTime::from_hms_opt(13, 0, 0).unwrap(),
);

#[pyfunction]
#[pyo3(signature = (base, second, minute, hour))]
pub fn py_time_before(base: StrOrT<NaiveTime>, second: u64, minute: u64, hour: u64) -> NaiveTime {
    time_before_inner(
        base.inner,
        Duration::from_secs(second + minute * 60 + hour * 3600),
    )
}

fn time_before_inner(base: NaiveTime, diff: Duration) -> NaiveTime {
    let origin_range = TimeRange::new(base - diff, base);
    if let Some(interset) = interset(RANGE, origin_range) {
        (RANGE.start).min(origin_range.start) - (interset.end - interset.start)
    } else {
        origin_range.start
    }
}

fn interset(r: TimeRange, other: TimeRange) -> Option<TimeRange> {
    let start = r.start.max(other.start);
    let end = r.end.min(other.end);

    if start < end {
        Some(TimeRange::new(start, end))
    } else {
        None
    }
}

#[derive(Clone, Copy)]
struct TimeRange {
    start: NaiveTime,
    end: NaiveTime,
}

impl TimeRange {
    const fn new(start: NaiveTime, end: NaiveTime) -> Self {
        Self { start, end }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn time_before_assert(base: &str, diff: Duration, expected: &str) {
        let base = NaiveTime::parse_from_str(base, "%H:%M:%S").unwrap();
        let expected = NaiveTime::parse_from_str(expected, "%H:%M:%S").unwrap();
        let time = time_before_inner(base, diff);
        assert_eq!(time, expected);
    }

    #[test]
    fn time_before_test() {
        time_before_assert("13:00:00", Duration::from_secs(1800), "11:00:00");
        time_before_assert("13:10:00", Duration::from_secs(1800), "11:10:00");

        time_before_assert("13:30:00", Duration::from_secs(1800), "13:00:00");
        time_before_assert("13:40:00", Duration::from_secs(1800), "13:10:00");
        time_before_assert("13:20:00", Duration::from_secs(1800), "11:20:00");

        time_before_assert("11:30:00", Duration::from_secs(1800), "11:00:00");
        time_before_assert("11:50:00", Duration::from_secs(1800), "11:00:00");
    }
}
