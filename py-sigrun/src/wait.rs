use backon::{ExponentialBuilder, Retryable};
use dyconfig::get_single_config;
use pyo3::pyfunction;
use reqwest::{Client, Url};
use runtime::current;
use std::{sync::LazyLock, time::Duration};
use tokio::time::sleep;

static POLLER: LazyLock<Poller> = LazyLock::new(|| {
    current().block_on(async move {
        let cfg: Url = get_single_config("app.sigrun.sdk", "virgo_ready_url", None)
            .await
            .unwrap();
        Poller {
            client: Client::new(),
            host: cfg,
        }
    })
});

struct Poller {
    client: Client,
    host: Url,
}

impl Poller {
    async fn async_wait_virgo_ready(
        &self,
        table_name: &str,
        options: &str,
        wait_time: Option<&str>,
        interval: f32,
    ) -> anyhow::Result<()> {
        #[derive(serde::Deserialize)]
        struct Response {
            status: bool,
        }

        let fetch = || {
            self.client
                .post(self.host.clone())
                .query(&[
                    ("table_name", table_name),
                    ("options", options),
                    ("time", wait_time.unwrap_or("")),
                ])
                .timeout(Duration::from_secs(100))
                .send()
        };

        loop {
            let response = fetch
                .retry(ExponentialBuilder::default())
                .notify(|e, d| {
                    tracing::error!("retrying error {e:?} with sleeping {} s", d.as_secs_f32());
                })
                .await?;

            if response.status().is_success() {
                let json: Response = response.json().await?;
                if json.status {
                    tracing::info!("Table {} is ready", table_name);
                    return Ok(());
                }
            }

            sleep(Duration::from_secs_f32(interval)).await;
            tracing::info!("Table {} is not ready", table_name);
        }
    }
}

#[pyfunction]
#[pyo3(signature = (table_name, options, interval_sec, wait_time=None))]
pub fn py_wait_virgo_ready(
    table_name: &str,
    options: &str,
    interval_sec: f32,
    wait_time: Option<&str>,
) -> anyhow::Result<()> {
    let poller = &*POLLER;
    current().block_on(async move {
        poller
            .async_wait_virgo_ready(table_name, options, wait_time, interval_sec)
            .await
    })
}
