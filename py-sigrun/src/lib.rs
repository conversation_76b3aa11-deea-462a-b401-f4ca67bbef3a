use crate::extension::py_with_columns_expr;
use alert_client::alert_tracing::otel_layer;
use pyo3::prelude::*;
use run::ErrorHandling;
use runtime::current;
use rustls::crypto;
use sigrun_context::{PythonContext, RustContext};
use sigrun_virgo::highfreq::{PyDataType, PyHighFreqReader, PySecurityType};
use sigrun_virgo::stream::{PyWindowsType, WantWindows};
use sigrun_virgo::{market::PyTradingDays, table::PyClient};
use std::num::NonZero;
use std::sync::OnceLock;
use std::thread::available_parallelism;
use tracing_subscriber::filter::filter_fn;
use tracing_subscriber::{
    Layer, fmt::time::ChronoLocal, layer::SubscriberExt, util::SubscriberInitExt,
};

mod extension;
mod infer_source;
mod run;
mod run_stream;
mod time_utils;
mod wait;

pub use pypolars::*;

#[pymodule]
pub fn sigrun(py: Python<'_>, m: &Bound<'_, PyModule>) -> PyResult<()> {
    setup_parallelism();

    init_crypto_provider();
    init_trace();

    setup_polars(py, m)?;

    m.add_function(wrap_pyfunction!(force_flush, m)?)?;

    m.add_function(wrap_pyfunction!(py_with_columns_expr, m)?)?;

    m.add_function(wrap_pyfunction!(run::py_drive, m)?)?;
    m.add_function(wrap_pyfunction!(run::py_drive_with_dates, m)?)?;
    m.add_function(wrap_pyfunction!(run::py_drive_all, m)?)?;
    m.add_function(wrap_pyfunction!(run::py_drive_all_with_dates, m)?)?;
    m.add_function(wrap_pyfunction!(infer_source::py_infer_schema, m)?)?;
    m.add_function(wrap_pyfunction!(infer_source::py_infer_source, m)?)?;
    m.add_function(wrap_pyfunction!(wait::py_wait_virgo_ready, m)?)?;
    m.add_function(wrap_pyfunction!(time_utils::py_to_timedelta, m)?)?;
    m.add_function(wrap_pyfunction!(time_utils::py_time_before, m)?)?;
    m.add_function(wrap_pyfunction!(sigrun_virgo::stream::order_level, m)?)?;
    m.add_function(wrap_pyfunction!(sigrun_virgo::stream::high_freq_bar, m)?)?;
    m.add_function(wrap_pyfunction!(sigrun_virgo::stream::py_high_freq, m)?)?;
    m.add_function(wrap_pyfunction!(run_stream::py_run_stream, m)?)?;

    m.add_class::<PyClient>()?;
    m.add_class::<PyTradingDays>()?;
    m.add_class::<PyHighFreqReader>()?;
    m.add_class::<PySecurityType>()?;
    m.add_class::<PyDataType>()?;
    m.add_class::<PythonContext>()?;
    m.add_class::<RustContext>()?;
    m.add_class::<ErrorHandling>()?;
    m.add_class::<WantWindows>()?;
    m.add_class::<PyWindowsType>()?;

    Ok(())
}

#[pyfunction]
fn force_flush() {
    if let Some(provider) = alert_client::alert_tracing::get_provider() {
        let _ = provider
            .force_flush()
            .inspect_err(|e| tracing::error!("failed to flush: {}", e));
    }
}

fn setup_parallelism() {
    if std::env::var("POLARS_MAX_THREADS").is_err() {
        let num_cpus = available_parallelism().unwrap_or(NonZero::new(1).unwrap());

        let parallelism = num_cpus.get() * 2;

        unsafe {
            std::env::set_var("POLARS_MAX_THREADS", parallelism.to_string());
        }
    }
}

fn setup_polars(py: Python<'_>, m: &Bound<'_, PyModule>) -> PyResult<()> {
    let polars_module = PyModule::new(py, "polars")?;
    polars(py, &polars_module)?;
    m.add_submodule(&polars_module)?;

    Ok(())
}

fn init_crypto_provider() {
    static PROVIDER: OnceLock<()> = OnceLock::new();
    PROVIDER.get_or_init(|| {
        let _ = crypto::aws_lc_rs::default_provider().install_default();
    });
}

fn init_trace() {
    static LOG: OnceLock<()> = OnceLock::new();
    LOG.get_or_init(|| {
        let registry = tracing_subscriber::registry();

        let filter = tracing_subscriber::EnvFilter::builder()
            .with_default_directive("info".parse().unwrap())
            .from_env_lossy();

        let log = tracing_subscriber::fmt::layer()
            .with_file(false)
            .with_timer(ChronoLocal::rfc_3339())
            .with_filter(filter)
            .with_filter(filter_fn(|me| me.is_event()));

        let registry = registry.with(log);

        let trace = trace_enabled();

        if trace {
            let otel = current().block_on(otel_layer());
            let _ = registry.with(otel).try_init();

            tracing::info!("enable tracing");
        } else {
            let _ = registry.try_init();
        }
    });
}

fn trace_enabled() -> bool {
    std::env::var("ARGUS_TRACING")
        .ok()
        .filter(|s| s == "true")
        .is_some()
}
