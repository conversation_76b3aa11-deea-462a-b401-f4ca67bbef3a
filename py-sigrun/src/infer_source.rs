use std::sync::Arc;

use chrono::{NaiveDate, NaiveDateTime, NaiveTime};
use polars::prelude::{AnonymousScan, DslPlan, FileScanDsl};
use polars_python::lazyframe::PyLazyFrame;
use pyo3::{Bound, PyResult, Python, exceptions::PyValueError, types::PyDict};
use pyo3_utils::StrOrT;
use serde::{Serialize, ser::SerializeMap};
use serde_json::Value;
use sigrun_io_expr::batch::{Injector, IoParam};
use sigrun_loader::func::{Function, PyLdfWrapper, parametrize::Markers};
use sigrun_virgo::table::VirgoScan;

#[pyo3::pyfunction]
#[pyo3(signature = (func, markers=None))]
pub fn py_infer_schema<'py>(
    func: Function,
    markers: Option<Markers>,
    py: Python<'py>,
) -> PyResult<Bound<'py, PyDict>> {
    func.run::<PyLdfWrapper<PyLazyFrame>>(schema_datetime(), markers.as_ref(), py)
        .next()
        .ok_or_else(|| PyValueError::new_err("this function does not return any layzframe"))??
        .collect_schema(py)
}

#[pyo3::pyfunction]
#[pyo3(signature = (func, filter_high_freq, date=None, time=None, markers=None))]
pub fn py_infer_source(
    func: Function,
    filter_high_freq: bool,
    date: Option<StrOrT<NaiveDate>>,
    time: Option<StrOrT<NaiveTime>>,
    markers: Option<Markers>,
    py: Python<'_>,
) -> PyResult<Vec<(String, String)>> {
    let ctx = NaiveDateTime::new(
        date.unwrap_or_else(schema_date).inner,
        time.unwrap_or_else(schema_time).inner,
    );
    let lf = func
        .run::<PyLdfWrapper<PyLazyFrame>>(ctx, markers.as_ref(), py)
        .next()
        .ok_or_else(|| PyValueError::new_err("this function does not return any layzframe"))??;

    Ok(extract_sources(&lf.ldf.logical_plan, filter_high_freq).collect())
}

fn extract_sources(
    plan: &DslPlan,
    filter_high_freq: bool,
) -> impl Iterator<Item = (String, String)> + '_ {
    plan.into_iter().filter_map(move |node| match node {
        DslPlan::Scan { scan_type, .. } => match &**scan_type {
            FileScanDsl::Anonymous { function, .. } => infer_table_info(function, filter_high_freq),
            _ => None,
        },
        _ => None,
    })
}

fn infer_table_info(
    anonymous: &Arc<dyn AnonymousScan>,
    filter_high_freq: bool,
) -> Option<(String, String)> {
    if let Some(virgo_scan) = anonymous.as_any().downcast_ref::<VirgoScan>() {
        Some((
            virgo_scan.table_name().to_string(),
            serde_json::to_string(&TruncateDateRange::new(virgo_scan.opt())).unwrap(),
        ))
    } else if let Some(injector) = anonymous.as_any().downcast_ref::<Injector>() {
        match injector.params() {
            IoParam::Table { name, opt, .. } => Some((
                name.clone(),
                serde_json::to_string(&TruncateDateRange::new(opt)).unwrap(),
            )),
            IoParam::HighFreq { param, .. } => {
                if filter_high_freq {
                    None
                } else {
                    Some((param.to_string(), String::new()))
                }
            }
        }
    } else {
        None
    }
}

const fn schema_date() -> StrOrT<NaiveDate> {
    StrOrT {
        inner: NaiveDate::from_ymd_opt(2024, 11, 11).unwrap(),
    }
}

const fn schema_time() -> StrOrT<NaiveTime> {
    StrOrT {
        inner: NaiveTime::from_hms_opt(9, 0, 0).unwrap(),
    }
}

const fn schema_datetime() -> NaiveDateTime {
    NaiveDateTime::new(
        NaiveDate::from_ymd_opt(2024, 11, 11).unwrap(),
        NaiveTime::from_hms_opt(9, 0, 0).unwrap(),
    )
}

pub struct TruncateDateRange<'a> {
    v: &'a serde_json::Map<String, Value>,
}

impl<'a> TruncateDateRange<'a> {
    pub fn new(v: &'a serde_json::Map<String, Value>) -> Self {
        Self { v }
    }
}

impl Serialize for TruncateDateRange<'_> {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        let mut map = serializer.serialize_map(Some(self.v.len()))?;
        let mut end_date = None;
        let mut start_date = None;
        for (k, v) in self.v.iter() {
            if k == "start_date" {
                start_date = Some(v);
            } else if k == "end_date" {
                end_date = Some(v);
            } else {
                map.serialize_entry(k, v)?;
            }
        }

        match (start_date, end_date) {
            (Some(_), Some(end_date)) => {
                map.serialize_entry("start_date", &end_date)?;
                map.serialize_entry("end_date", &end_date)?;
            }
            (None, None) => (),
            (None, Some(d)) => map.serialize_entry("end_date", &d)?,
            (Some(d), None) => map.serialize_entry("start_date", &d)?,
        };

        map.end()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn compare_json(v: &Value, expected: Value) {
        let truncated = TruncateDateRange::new(v.as_object().unwrap());
        let serialized = serde_json::to_string(&truncated).unwrap();
        assert_eq!(
            serde_json::from_str::<Value>(&serialized).unwrap(),
            expected
        );
    }

    #[test]
    fn test_truncate_date_range() {
        let v = serde_json::json!({
            "start_date": "2024-01-01",
            "end_date": "2024-01-02",
            "other": "other"
        });

        let expected = serde_json::json!({
            "other": "other",
            "start_date": "2024-01-02",
            "end_date": "2024-01-02"
        });

        compare_json(&v, expected);

        let v = serde_json::json!({
            "end_date": "2024-01-02",
            "start_date": "2024-01-01",
            "other": "other"
        });

        let expected = serde_json::json!({
            "other": "other",
            "end_date": "2024-01-02",
            "start_date": "2024-01-02",
        });

        compare_json(&v, expected);

        let v = serde_json::json!({
            "end_date": "2024-01-02",
            "other": "other"
        });

        let expected = serde_json::json!({
            "other": "other",
            "end_date": "2024-01-02"
        });

        compare_json(&v, expected);

        let v = serde_json::json!({
            "start_date": "2024-01-01",
            "other": "other"
        });

        let expected = serde_json::json!({
            "other": "other",
            "start_date": "2024-01-01",
        });

        compare_json(&v, expected);

        let v = serde_json::json!({
            "other": "other"
        });

        let expected = serde_json::json!({
            "other": "other",
        });

        compare_json(&v, expected);
    }
}
