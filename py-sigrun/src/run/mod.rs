use std::{thread::available_parallelism, time::Instant};

use chrono::{NaiveDate, NaiveDateTime, NaiveTime};
use datetime_iterator::{date_time_iter, trading_time};
use opentelemetry::{Context, trace::TraceContextExt};
use polars::{
    error::PolarsError,
    frame::DataFrame,
    prelude::{Column, Lazy<PERSON>rame, Scalar},
};
use polars_core::utils::accumulate_dataframes_vertical;
use polars_python::{dataframe::PyDataFrame, error::PyPolarsErr, lazyframe::PyLazyFrame};
use progress_bar::{
    AsyncProgressBar, ProgressBar, finish, stage_building_lazyframes, stage_executing_lazyframes,
};
use pyo3::{PyErr, PyResult, Python, pyclass, types::PyAnyMethods};
use pyo3_otel::PySpanContext;
use pyo3_utils::StrOrTList;
use rayon::iter::IntoParallelIterator;
use runtime::current;
use sigrun_engine::batch::{FuncIdx, Plan, Register};
use sigrun_loader::func::{Function, PyLdfWrapper, parametrize::Markers};
use sigrun_virgo::table::PyClient;
use tracing::{Span, info_span, instrument, span::EnteredSpan};
use tracing_opentelemetry::OpenTelemetrySpanExt;
use virgo::market::trading_days::TradingDays;

mod progress_bar;

#[pyclass(eq, eq_int)]
#[derive(Clone, Copy, Debug, PartialEq)]
pub enum ErrorHandling {
    Failed,
    FillNull,
    Ignore,
}

#[allow(clippy::too_many_arguments)]
#[pyo3::pyfunction]
#[pyo3(signature = (func, dates, quiet, error_handling, virgo, ctx, times=None, markers=None, limit=None))]
pub fn py_drive_with_dates(
    func: Function,
    dates: Vec<NaiveDate>,
    quiet: bool,
    error_handling: ErrorHandling,
    virgo: PyClient,
    ctx: PySpanContext,
    times: Option<StrOrTList<NaiveTime>>,
    markers: Option<Markers>,
    limit: Option<usize>,
    py: Python<'_>,
) -> PyResult<PyDataFrame> {
    let schedule = build_schedule_with_dates(dates, times);

    let _g = propagate(ctx, info_span!("rust run with dates"));

    drive_inner(
        &func,
        schedule.clone(),
        quiet,
        markers.as_ref(),
        error_handling,
        virgo,
        limit,
        py,
    )
}

#[allow(clippy::too_many_arguments)]
#[pyo3::pyfunction]
#[pyo3(signature = (func, start_date, end_date, trading_day, quiet, error_handling, virgo, ctx, times=None, markers=None, limit=None))]
pub fn py_drive(
    func: Function,
    start_date: NaiveDate,
    end_date: NaiveDate,
    trading_day: bool,
    quiet: bool,
    error_handling: ErrorHandling,
    virgo: PyClient,
    ctx: PySpanContext,
    times: Option<StrOrTList<NaiveTime>>,
    markers: Option<Markers>,
    limit: Option<usize>,
    py: Python<'_>,
) -> PyResult<PyDataFrame> {
    let schedule = build_schedule(trading_day, start_date, end_date, times)?;

    let _g = propagate(ctx, info_span!("rust run"));

    drive_inner(
        &func,
        schedule.clone(),
        quiet,
        markers.as_ref(),
        error_handling,
        virgo,
        limit,
        py,
    )
}

#[allow(clippy::too_many_arguments)]
#[pyo3::pyfunction]
#[pyo3(signature = (funcs, dates, quiet, error_handling, virgo, times=None, limit=None))]
pub fn py_drive_all_with_dates(
    funcs: Vec<Function>,
    dates: Vec<NaiveDate>,
    quiet: bool,
    error_handling: ErrorHandling,
    virgo: PyClient,
    times: Option<StrOrTList<NaiveTime>>,
    limit: Option<usize>,
    py: Python<'_>,
) -> PyResult<Vec<PyDataFrame>> {
    let schedule = build_schedule_with_dates(dates, times);

    drive_all_inner(
        &funcs,
        schedule.clone(),
        quiet,
        error_handling,
        virgo,
        limit,
        py,
    )
}

#[allow(clippy::too_many_arguments)]
#[pyo3::pyfunction]
#[pyo3(signature = (funcs, start_date, end_date, trading_day, quiet, error_handling, virgo, times=None, limit=None))]
pub fn py_drive_all(
    funcs: Vec<Function>,
    start_date: NaiveDate,
    end_date: NaiveDate,
    trading_day: bool,
    quiet: bool,
    error_handling: ErrorHandling,
    virgo: PyClient,
    times: Option<StrOrTList<NaiveTime>>,
    limit: Option<usize>,
    py: Python<'_>,
) -> PyResult<Vec<PyDataFrame>> {
    let schedule = build_schedule(trading_day, start_date, end_date, times)?;

    drive_all_inner(
        &funcs,
        schedule.clone(),
        quiet,
        error_handling,
        virgo,
        limit,
        py,
    )
}

#[allow(clippy::too_many_arguments)]
fn drive_all_inner<S>(
    funcs: &[Function],
    schedule: S,
    quiet: bool,
    error_handling: ErrorHandling,
    virgo: PyClient,
    limit: Option<usize>,
    py: Python<'_>,
) -> PyResult<Vec<PyDataFrame>>
where
    S: IntoParallelIterator<Item = NaiveDateTime> + IntoIterator<Item = NaiveDateTime> + Clone,
{
    let started = Instant::now();

    let jupyter = is_jupyter(py);

    let cnt = schedule.clone().into_iter().count() * funcs.len();
    if cnt >= 30 {
        tracing::info!("enable dir cache");
        virgo::highfreq::history::enable_dir_cache();
    } else {
        tracing::info!("disable dir cache");
    }

    let building_stage = stage_building_lazyframes(py, cnt as u64, quiet, jupyter);

    let ldfs: Vec<_> = funcs
        .iter()
        .enumerate()
        .flat_map(|(idx, f)| {
            let iter = schedule.clone().into_iter().inspect(|_| {
                building_stage.inc(py);
            });
            f.batch_run::<_, PyLdfWrapper<PyLazyFrame>>(iter, None, py)
                .map(move |item| {
                    item.map(|(dt, lf)| Plan {
                        inner: lf.inner.ldf,
                        dt,
                        func_idx: FuncIdx::from(idx),
                    })
                })
        })
        .collect::<Result<_, _>>()?;

    building_stage.finish_and_clear(py);

    py.allow_threads(|| {
        let len = ldfs.len();

        let execution_stage = stage_executing_lazyframes(quiet, len as u64, jupyter);
        execution_stage.tick();

        let re = Register::new(ldfs).map_err(PyPolarsErr::from)?;

        let dfs = re.run(
            virgo.client,
            limit.unwrap_or_else(|| paralleism() / 2),
            len,
            || {
                execution_stage.inc();
            },
        );

        let dfs = match error_handling {
            ErrorHandling::Failed => dfs
                .map(|(r, idx)| r.map(|df| (idx, df)).map_err(|(_, e)| PyPolarsErr::from(e)))
                .collect::<Result<Vec<_>, PyPolarsErr>>()?,
            ErrorHandling::FillNull => fill_nan_when_error(dfs.collect(), |df, idx| (idx, df))
                .map_err(PyPolarsErr::from)?,
            ErrorHandling::Ignore => dfs
                .filter_map(|(item, idx)| item.ok().map(|df| (idx, df)))
                .collect(),
        };

        let df_list = SplitBucket::from(&dfs)
            .map(|bucket| {
                if bucket.is_empty() {
                    Ok(PyDataFrame {
                        df: DataFrame::empty(),
                    })
                } else {
                    accumulate_dataframes_vertical(bucket.iter().map(|(_, df)| df.clone()))
                        .map_err(|e| PyPolarsErr::from(e).into())
                        .map(|df| PyDataFrame { df })
                }
            })
            .collect::<PyResult<Vec<_>>>()?;

        finish(quiet, started);

        Ok(df_list)
    })
}

#[allow(clippy::too_many_arguments)]
fn drive_inner<S>(
    func: &Function,
    schedule: S,
    quiet: bool,
    markers: Option<&Markers>,
    error_handling: ErrorHandling,
    virgo: PyClient,
    limit: Option<usize>,
    py: Python<'_>,
) -> PyResult<PyDataFrame>
where
    S: IntoParallelIterator<Item = NaiveDateTime> + IntoIterator<Item = NaiveDateTime> + Clone,
{
    let started = Instant::now();

    let jupyter = is_jupyter(py);

    let ldfs = building(schedule, quiet, jupyter, func, markers, py)?;

    py.allow_threads(|| {
        let ret = executing(ldfs, quiet, jupyter, error_handling, virgo, limit);

        finish(quiet, started);

        ret
    })
}

#[instrument(skip_all)]
fn executing(
    ldfs: Vec<Plan<LazyFrame>>,
    quiet: bool,
    jupyter: bool,
    error_handling: ErrorHandling,
    virgo: PyClient,
    limit: Option<usize>,
) -> PyResult<PyDataFrame> {
    let len = ldfs.len();

    let execution_stage = stage_executing_lazyframes(quiet, len as u64, jupyter);
    execution_stage.tick();

    let re = Register::new(ldfs).map_err(PyPolarsErr::from)?;

    let dfs = re.run(
        virgo.client,
        limit.unwrap_or_else(|| paralleism() / 2),
        len,
        || {
            execution_stage.inc();
        },
    );

    // TODO: remove collect in error_handling
    let dfs = match error_handling {
        ErrorHandling::Failed => dfs
            .map(|(item, _)| item.map_err(|(_, e)| PyPolarsErr::from(e)))
            .collect::<Result<Vec<_>, PyPolarsErr>>()?,
        ErrorHandling::FillNull => {
            fill_nan_when_error(dfs.collect(), |df, _| df).map_err(PyPolarsErr::from)?
        }
        ErrorHandling::Ignore => dfs.filter_map(|(item, _)| item.ok()).collect(),
    };

    let df = if dfs.is_empty() {
        DataFrame::empty()
    } else {
        accumulate_dataframes_vertical(dfs).map_err(PyPolarsErr::from)?
    };

    Ok(PyDataFrame { df })
}

#[instrument(skip_all)]
fn building<S>(
    schedule: S,
    quiet: bool,
    jupyter: bool,
    func: &Function,
    markers: Option<&Markers>,
    py: Python<'_>,
) -> PyResult<Vec<Plan<LazyFrame>>>
where
    S: IntoParallelIterator<Item = NaiveDateTime> + IntoIterator<Item = NaiveDateTime> + Clone,
{
    let cnt = schedule.clone().into_iter().count();
    if cnt >= 30 {
        tracing::info!("enable dir cache");
        virgo::highfreq::history::enable_dir_cache();
    } else {
        tracing::info!("disable dir cache");
    }

    let building_stage = stage_building_lazyframes(py, cnt as u64, quiet, jupyter);
    let iter = schedule.into_iter().inspect(|_| {
        building_stage.inc(py);
    });

    let ldfs: Vec<_> = func
        .batch_run::<_, PyLdfWrapper<PyLazyFrame>>(iter, markers, py)
        .map(|item| {
            item.map(|(dt, lf)| Plan {
                inner: lf.inner.ldf,
                dt,
                func_idx: FuncIdx::from(0),
            })
        })
        .collect::<Result<_, _>>()?;

    building_stage.finish_and_clear(py);

    Ok(ldfs)
}

#[allow(clippy::type_complexity)]
fn fill_nan_when_error<T, F, R>(
    input: Vec<(Result<DataFrame, (NaiveDateTime, PolarsError)>, T)>,
    handle: F,
) -> Result<Vec<R>, PolarsError>
where
    F: Fn(DataFrame, T) -> R,
{
    let first = input
        .iter()
        .find_map(|(item, _)| item.as_ref().map(|df| df.schema().clone()).ok());

    let Some(first) = first else {
        let (_, first_err) = input.into_iter().find_map(|(item, _)| item.err()).unwrap();
        tracing::error!("all lazyframes failed, return the first error");
        return Err(first_err);
    };

    let make_default_df = |dt: NaiveDateTime| {
        let default_df: Vec<_> = first
            .iter()
            .map(|(name, _)| match name.as_str() {
                "date" => Column::new(name.clone(), vec![dt.date()]),
                "time" => Column::new(name.clone(), vec![dt.time()]),
                _ => Column::new_scalar(name.clone(), Scalar::default(), 1),
            })
            .collect();

        unsafe { DataFrame::new_no_checks(1, default_df) }
    };

    Ok(input
        .into_iter()
        .map(|(item, idx)| {
            let df = item.unwrap_or_else(|(dt, _)| make_default_df(dt));
            handle(df, idx)
        })
        .collect())
}

fn is_jupyter(py: Python<'_>) -> bool {
    let f = || {
        let ipython = py.import("IPython")?.getattr("get_ipython")?.call0()?;

        Ok::<_, PyErr>(!ipython.is_none())
    };

    f().unwrap_or(false)
}

fn build_schedule(
    trading_day: bool,
    start_date: NaiveDate,
    end_date: NaiveDate,
    times: Option<StrOrTList<NaiveTime>>,
) -> anyhow::Result<
    impl IntoParallelIterator<Item = NaiveDateTime> + IntoIterator<Item = NaiveDateTime> + Clone,
> {
    // TODO: box iterator
    let trading_days = if trading_day {
        trading_days()?
    } else {
        TradingDays::new(
            start_date
                .iter_days()
                .take_while(|x| x <= &end_date)
                .collect(),
        )
    };

    let times = times
        .map(|t| t.inner)
        .unwrap_or_else(|| Vec::from(trading_time()));

    Ok(date_time_iter(
        trading_days.ranges(start_date..=end_date),
        times,
    ))
}

fn build_schedule_with_dates(
    dates: impl IntoIterator<Item = NaiveDate> + IntoParallelIterator<Item = NaiveDate> + Clone,
    times: Option<StrOrTList<NaiveTime>>,
) -> impl IntoParallelIterator<Item = NaiveDateTime> + IntoIterator<Item = NaiveDateTime> + Clone {
    let times = times
        .map(|t| t.inner)
        .unwrap_or_else(|| Vec::from(trading_time()));

    date_time_iter(dates, times)
}

fn trading_days() -> anyhow::Result<TradingDays> {
    let rt = current();
    let client = virgo::market::MarketClient::new("http://virgo-market.sci-inv.cn");

    rt.block_on(client.trading_days())
}

fn paralleism() -> usize {
    available_parallelism().map(|p| p.get()).unwrap_or(16)
}

struct SplitBucket<'a, I, R> {
    input: &'a [(I, R)],
    current: usize,
}

impl<'a, I, R, T> From<&'a T> for SplitBucket<'a, I, R>
where
    T: AsRef<[(I, R)]>,
    I: PartialEq + Eq,
{
    fn from(value: &'a T) -> Self {
        Self {
            input: value.as_ref(),
            current: 0,
        }
    }
}

impl<'a, I, R> Iterator for SplitBucket<'a, I, R>
where
    I: PartialEq + Eq,
{
    type Item = &'a [(I, R)];

    fn next(&mut self) -> Option<Self::Item> {
        if self.current >= self.input.len() {
            return None;
        }

        let start = self.current;
        let mut end = start + 1;

        while end < self.input.len() && self.input[start].0 == self.input[end].0 {
            end += 1;
        }

        self.current = end;
        Some(&self.input[start..end])
    }
}

fn propagate(ctx: PySpanContext, span: Span) -> EnteredSpan {
    let ctx = Context::map_current(|cur| cur.with_remote_span_context(ctx.ctx));

    span.set_parent(ctx);

    span.entered()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_split_bucket() {
        let input: Vec<(i32, &str)> = vec![];
        let result: Vec<_> = SplitBucket::from(&input).collect();
        assert!(result.is_empty());

        let input = vec![(1, "a")];
        let result: Vec<_> = SplitBucket::from(&input).collect();
        assert_eq!(result.len(), 1);
        assert_eq!(result[0], &[(1, "a")]);

        let input = vec![(1, "a"), (1, "b"), (1, "c")];
        let result: Vec<_> = SplitBucket::from(&input).collect();
        assert_eq!(result.len(), 1);
        assert_eq!(result[0], &[(1, "a"), (1, "b"), (1, "c")]);

        let input = vec![(1, "a"), (2, "b"), (3, "c")];
        let result: Vec<_> = SplitBucket::from(&input).collect();
        assert_eq!(result.len(), 3);
        assert_eq!(result[0], &[(1, "a")]);
        assert_eq!(result[1], &[(2, "b")]);
        assert_eq!(result[2], &[(3, "c")]);

        let input = vec![(1, "a"), (1, "b"), (2, "c"), (2, "d"), (3, "e")];
        let result: Vec<_> = SplitBucket::from(&input).collect();
        assert_eq!(result.len(), 3);
        assert_eq!(result[0], &[(1, "a"), (1, "b")]);
        assert_eq!(result[1], &[(2, "c"), (2, "d")]);
        assert_eq!(result[2], &[(3, "e")]);
    }
}
