use super::{AsyncProgressBar, ProgressBar};
use pyo3::Python;
use std::{sync::mpsc, thread::<PERSON><PERSON><PERSON><PERSON><PERSON>};
use tqdm_rs::ProgressBar as JupyterBar;

pub struct AsyncJupyter {
    tx: mpsc::Sender<()>,
}

impl AsyncJupyter {
    pub fn new(cnt: u64) -> Self {
        let (tx, rx) = mpsc::channel();
        render_thread(rx, cnt);
        AsyncJupyter { tx }
    }
}

fn render_thread(rx: mpsc::Receiver<()>, cnt: u64) -> Join<PERSON><PERSON>le<()> {
    std::thread::spawn(move || {
        let pb = Python::with_gil(|py| JupyterBar::new(py, cnt).unwrap());

        while rx.recv().is_ok() {
            Python::with_gil(|py| {
                let _ = pb.inc(1, py);
            });
        }

        Python::with_gil(|py| {
            let _ = pb.finish(py);
        });
    })
}

impl AsyncProgressBar for AsyncJupyter {
    fn inc(&self) {
        self.tx.send(()).unwrap();
    }

    fn tick(&self) {}
}

pub struct Jupyter {
    inner: JupyterBar,
}

impl ProgressBar for Jupyter {
    fn finish_and_clear(self, py: Python<'_>) {
        let _ = self.inner.finish(py);
    }

    fn inc(&self, py: Python<'_>) {
        let _ = self.inner.inc(1, py);
    }
}

impl Jupyter {
    pub fn new(cnt: u64, py: Python<'_>) -> Self {
        Jupyter {
            inner: JupyterBar::new(py, cnt).unwrap(),
        }
    }
}
