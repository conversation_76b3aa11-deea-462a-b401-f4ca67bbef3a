use console::{Emoji, style};
use enum_dispatch::enum_dispatch;
use indicatif::HumanDuration;
use jupyter::{As<PERSON>Jupyte<PERSON>, <PERSON><PERSON><PERSON>};
use noop::Noop;
use pyo3::Python;
use std::time::Instant;
use text::Indicatif;

static LOOKING_GLASS: Emoji<'_, '_> = Emoji("🔍  ", "");
static TRUCK: Emoji<'_, '_> = Emoji("🚚  ", "");
static SPARKLE: Emoji<'_, '_> = Emoji("✨ ", ":-)");

mod jupyter;
mod noop;
mod text;

#[enum_dispatch(BarEnum)]
pub trait ProgressBar {
    fn finish_and_clear(self, py: Python<'_>);

    fn inc(&self, py: Python<'_>);
}

#[enum_dispatch(AsyncBarEnum)]
pub trait AsyncProgressBar {
    fn inc(&self);

    fn tick(&self);
}

#[enum_dispatch]
pub enum AsyncBarEnum {
    Noop(Noop),
    Text(Indicatif),
    Jupyter(AsyncJupyter),
}

#[enum_dispatch]
pub enum BarEnum {
    Noop(Noop),
    Text(Indicatif),
    Jupy<PERSON>(Jupyter),
}

pub fn finish(quiet: bool, started: Instant) {
    if !quiet {
        println!("{} Done in {}", SPARKLE, HumanDuration(started.elapsed()));
    }
}

pub fn stage_building_lazyframes(py: Python<'_>, cnt: u64, quiet: bool, jupyter: bool) -> BarEnum {
    if quiet {
        return Noop.into();
    }
    println!(
        "{} {}Building  lazyframes...",
        style("[1/2]").bold().dim(),
        LOOKING_GLASS
    );

    if jupyter {
        Jupyter::new(cnt, py).into()
    } else {
        Indicatif::new(cnt).into()
    }
}

pub fn stage_executing_lazyframes(quiet: bool, cnt: u64, jupyter: bool) -> AsyncBarEnum {
    if quiet {
        return Noop.into();
    }
    println!(
        "{} {}Executing lazyframes...",
        style("[2/2]").bold().dim(),
        TRUCK
    );

    if jupyter {
        AsyncJupyter::new(cnt).into()
    } else {
        Indicatif::new(cnt).into()
    }
}
