use super::{AsyncProgressBar, ProgressBar};
use indicatif::ProgressBar as TextBar;
use pyo3::Python;

pub struct Indicatif {
    inner: TextBar,
}

impl AsyncProgressBar for Indicatif {
    fn inc(&self) {
        self.inner.inc(1);
    }

    fn tick(&self) {
        self.inner.tick();
    }
}

impl Indicatif {
    pub fn new(cnt: u64) -> Self {
        Indicatif {
            inner: TextBar::new(cnt),
        }
    }
}

impl ProgressBar for Indicatif {
    fn finish_and_clear(self, _: Python<'_>) {
        self.inner.finish_and_clear();
    }

    fn inc(&self, _: Python<'_>) {
        self.inner.inc(1);
    }
}
