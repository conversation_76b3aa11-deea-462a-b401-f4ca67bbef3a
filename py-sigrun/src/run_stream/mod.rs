use std::ops::Deref;

use chrono::{Duration, NaiveDate, NaiveTime};
use polars::{frame::DataFrame, time::ClosedWindow};
use polars_core::utils::accumulate_dataframes_vertical;
use polars_python::{dataframe::PyDataFrame, error::PyPolarsErr, prelude::Wrap};
use pyo3::{PyResult, Python};
use pyo3_utils::StrOrT;
use runtime::current;
use sigrun_engine::streaming::runner::Runner;
use sigrun_loader::func::Function;
use sigrun_virgo::market::PyTradingDays;

use crate::run::ErrorHandling;

#[allow(clippy::too_many_arguments)]
#[pyo3::pyfunction]
#[pyo3(signature = (func, start_date, end_date, error_handling, trading_days, closed, start_time=None, end_time=None, limit=None))]
pub fn py_run_stream(
    func: Function,
    start_date: StrOrT<NaiveDate>,
    end_date: StrOrT<NaiveDate>,
    error_handling: ErrorHandling,
    trading_days: &PyTradingDays,
    closed: Wrap<ClosedWindow>,
    start_time: Option<StrOrT<NaiveTime>>,
    end_time: Option<StrOrT<NaiveTime>>,
    limit: Option<usize>,
    py: Python<'_>,
) -> PyResult<PyDataFrame> {
    let runner = Runner::default();

    let df = current().block_on(async move {
        let trading_days = trading_days
            .deref()
            .trading_days()
            .ranges(start_date.inner..=end_date.inner);

        let dfs = runner
            .run_history(
                py,
                func,
                limit.unwrap_or(8),
                &trading_days,
                closed_start(start_time.map(|t| t.inner), closed.0),
                closed_end(end_time.map(|t| t.inner), closed.0),
            )
            .await?;

        let dfs = match error_handling {
            ErrorHandling::Failed => dfs
                .map(|item| item.map_err(|(_, e)| PyPolarsErr::from(e)))
                .collect::<Result<Vec<_>, PyPolarsErr>>()?,
            ErrorHandling::FillNull => {
                todo!()
            }
            ErrorHandling::Ignore => todo!(),
        };

        let df = if dfs.is_empty() {
            DataFrame::empty()
        } else {
            accumulate_dataframes_vertical(dfs).map_err(PyPolarsErr::from)?
        };

        Ok::<_, anyhow::Error>(PyDataFrame { df })
    })?;

    Ok(df)
}

fn closed_start(start: Option<NaiveTime>, closed: ClosedWindow) -> Option<NaiveTime> {
    match closed {
        ClosedWindow::Right | ClosedWindow::None => start.map(|t| t + Duration::nanoseconds(1)),
        _ => start,
    }
}

fn closed_end(end: Option<NaiveTime>, closed: ClosedWindow) -> Option<NaiveTime> {
    match closed {
        ClosedWindow::Left | ClosedWindow::None => end.map(|t| t - Duration::nanoseconds(1)),
        _ => end,
    }
}
