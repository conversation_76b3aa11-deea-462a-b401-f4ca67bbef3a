use polars_exts::extensions::with_columns_expr;
use polars_python::expr::PyExpr;
use polars_python::lazyframe::PyLazyFrame;
use polars_python::{error::PyPolarsErr, expr::ToExprs};
use pyo3::{pyfunction, PyResult};

#[pyfunction]
pub fn py_with_columns_expr(ldf: PyLazyFrame, exprs: Vec<PyExpr>) -> PyResult<PyLazyFrame> {
    let ldf = with_columns_expr(ldf.ldf, exprs.to_exprs()).map_err(PyPolarsErr::from)?;
    Ok(ldf.into())
}
