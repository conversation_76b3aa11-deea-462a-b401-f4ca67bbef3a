[tasks.nextest]
script = '''
LD_LIBRARY_PATH="${CONDA_PREFIX}/lib" cargo nextest run ${@}
'''

[env]
"RUSTFLAGS" = { script = [
    "${CARGO_MAKE_WORKSPACE_WORKING_DIRECTORY}/script/merge_rustflags.sh",
] }
"CFLAGS" = "-msse3 -mssse3 -msse4.1 -msse4.2 -mpopcnt -mcx16 -mavx -mavx2 -mfma -mbmi -mbmi2 -mlzcnt -mpclmul -mmovbe -mtune=skylake"

[tasks.publish]
command = "maturin"
args = ["publish", "--repository-url", "http://pypi.sci-inv.cn", "${@}"]

[tasks.test-perf]
command = "maturin"
args = ["develop", "--profile", "release-thin", "${@}"]
