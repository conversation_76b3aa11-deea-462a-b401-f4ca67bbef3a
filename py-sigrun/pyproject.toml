[build-system]
requires = ["maturin>=1.4,<2.0"]
build-backend = "maturin"

[project]
name = "sigrun"
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Rust",
    "Programming Language :: Python :: Implementation :: CPython",
    "Programming Language :: Python :: Implementation :: PyPy",
]
dynamic = ["version"]
dependencies = ["apolars == 1.32.0b1", "sigrun-loader >= 0.3"]

[tool.maturin]
features = ["pyo3/extension-module"]
