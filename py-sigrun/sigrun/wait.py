from __future__ import annotations

from typing import TYPE_CHECKING

from sigrun.schema import infer_virgo_table_sources
from sigrun.sigrun import py_wait_virgo_ready

if TYPE_CHECKING:
    from datetime import date, time
    from typing import Callable

    from sigrun import Marker


def wait_until_ready(
    func: Callable,
    date: str | date | None = None,
    time: str | time | None = None,
    markers: list[Marker] | None = None,
):
    """
    等待指定函数所需的 Virgo Table 准备就绪。

    参数:
        func: 需要执行的可调用函数。
        date: 指定的日期。
        time: 指定的时间。
        markers: 标记列表。

    功能:
        此函数会分析指定函数所依赖的 Virgo Table , 并等待所有数据源准备就绪后再返回。
    """
    table_sources = infer_virgo_table_sources(func, date, time, markers)
    for table_name, options in table_sources:
        py_wait_virgo_ready(table_name, options, 0.5, time)
