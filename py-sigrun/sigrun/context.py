from __future__ import annotations

from enum import Enum
from typing import TYPE_CHECKING

import polars as pl

from sigrun.sigrun import PyClient, PythonContext, RustContext

if TYPE_CHECKING:
    import sys
    from datetime import timedelta

    if sys.version_info >= (3, 11):
        from typing import Self
    else:
        from typing_extensions import Self


# call in rust or local mode
def inject_python_context(ctx: PythonContext) -> None:
    global _python_context
    _python_context = ctx


def reset_context() -> None:
    global _python_context, _rust_context
    _python_context = None
    _rust_context = RustContext()


def python_context() -> PythonContextWrap:
    global _python_context

    if _python_context is not None:
        return PythonContextWrap(_python_context)

    if is_local():
        ctx = PythonContextWrap(rust_context())

        inject_python_context(ctx._inner)

        return ctx
    else:
        raise RuntimeError("virgo not init")


def rust_context() -> RustContext:
    global _rust_context
    return _rust_context


class Env(Enum):
    LOCAL = 1
    SERVER = 2


def is_local() -> bool:
    return _env == Env.LOCAL


# call in rust by sigrun-scheduler
def set_server_mode() -> None:
    global _env
    _env = Env.SERVER


class PythonContextWrap:
    _inner: PythonContext

    def __init__(self, inner: RustContext | PythonContext) -> None:
        if isinstance(inner, RustContext):
            self._inner = PythonContext(inner)
        elif isinstance(inner, PythonContext):
            self._inner = inner
        else:
            raise TypeError

    def get_virgo_client(self) -> _Client:
        return _Client.from_rs(self._inner.get_virgo_client())


class _Client:
    _inner: PyClient

    @classmethod
    def from_rs(cls, inner: PyClient) -> Self:
        c = _Client.__new__(cls)
        c._inner = inner
        return c

    def __init__(self, secret: str, value: str):
        self._inner = PyClient(secret, value)

    def read(
        self, table: str, timeout: timedelta | None = None, **options
    ) -> pl.LazyFrame:
        df = pl.DataFrame.__new__(pl.DataFrame)
        df._df = self._inner.read(table, timeout, options)
        return df.lazy()

    def scan(
        self, table: str, timeout: timedelta | None = None, **options
    ) -> pl.LazyFrame:
        lf = pl.LazyFrame.__new__(pl.LazyFrame)
        lf._ldf = self._inner.scan(table, timeout, options)
        return lf


_env = Env.LOCAL
_python_context: None | PythonContext = None
_rust_context: RustContext = RustContext()
