from __future__ import annotations

from datetime import date, time
from typing import TYPE_CHECKING

import polars as pl
from argus_tracing import trace
from sigrun_loader.scanner import parse_func

from sigrun.context import python_context
from sigrun.sigrun import (
    ErrorHandling,
    py_drive,
    py_drive_all,
    py_drive_all_with_dates,
    py_drive_with_dates,
    py_run_stream,
)
from sigrun.sigurn_io_context import SigrunIo
from sigrun.virgo.market import _TradingDays

tracer = trace.get_tracer(__name__)

if TYPE_CHECKING:
    from typing import Callable

    from sigrun import Marker
    from polars._typing import ClosedInterval


def failed() -> ErrorHandling:
    return ErrorHandling.Failed


def fill_null() -> ErrorHandling:
    return ErrorHandling.FillNull


def ignore() -> ErrorHandling:
    return ErrorHandling.Ignore


def run(
    func: Callable,
    start_date: date | str,
    end_date: date | str,
    trading_days: bool = True,
    quiet: bool = False,
    error_handling: ErrorHandling = failed(),
    times: list[time | str] | None = None,
    markers: list[Marker] | None = None,
    limit: int | None = None,
) -> pl.DataFrame:
    df = pl.DataFrame.__new__(pl.DataFrame)

    with tracer.start_as_current_span("run") as span:
        with SigrunIo():
            df._df = py_drive(
                parse_func(func),
                _parse_date(start_date),
                _parse_date(end_date),
                trading_days,
                quiet,
                error_handling,
                python_context().get_virgo_client()._inner,
                span.get_span_context(),
                times,
                markers,
                limit,
            )
            return df


def run_with_dates(
    func: Callable,
    dates: list[date | str],
    quiet: bool = False,
    error_handling: ErrorHandling = failed(),
    times: list[time | str] | None = None,
    markers: list[Marker] | None = None,
    limit: int | None = None,
) -> pl.DataFrame:
    df = pl.DataFrame.__new__(pl.DataFrame)

    with SigrunIo():
        df._df = py_drive_with_dates(
            parse_func(func),
            [_parse_date(d) for d in dates],
            quiet,
            error_handling,
            python_context().get_virgo_client()._inner,
            times,
            markers,
            limit,
        )
        return df


def _make_df(inner_df: pl.DataFrame) -> pl.DataFrame:
    df = pl.DataFrame.__new__(pl.DataFrame)
    df._df = inner_df
    return df


def run_stream(
    func: Callable,
    start_date: date | str,
    end_date: date | str,
    start_time: time | str | None = None,
    end_time: time | str | None = time(15),
    closed: ClosedInterval = "left",
    error_handling: ErrorHandling = failed(),
    limit: int | None = None,
):
    df = py_run_stream(
        parse_func(func),
        _parse_date(start_date),
        _parse_date(end_date),
        error_handling,
        _TradingDays.get_instance(),
        closed,
        start_time,
        end_time,
        limit,
    )

    return _make_df(df)


def run_all(
    funcs: list[Callable],
    start_date: date | str,
    end_date: date | str,
    trading_days: bool = True,
    quiet: bool = False,
    error_handling: ErrorHandling = failed(),
    times: list[time | str] | None = None,
    limit: int | None = None,
) -> list[pl.DataFrame]:
    with SigrunIo():
        inner_list = py_drive_all(
            [parse_func(func) for func in funcs],
            _parse_date(start_date),
            _parse_date(end_date),
            trading_days,
            quiet,
            error_handling,
            python_context().get_virgo_client()._inner,
            times,
            limit,
        )

        return [_make_df(rs_df) for rs_df in inner_list]


def run_all_with_dates(
    funcs: list[Callable],
    dates: list[date | str],
    quiet: bool = False,
    error_handling: ErrorHandling = failed(),
    times: list[time | str] | None = None,
    limit: int | None = None,
) -> list[pl.DataFrame]:
    with SigrunIo():
        inner_list = py_drive_all_with_dates(
            [parse_func(func) for func in funcs],
            [_parse_date(d) for d in dates],
            quiet,
            error_handling,
            python_context().get_virgo_client()._inner,
            times,
            limit,
        )

        return [_make_df(rs_df) for rs_df in inner_list]


def _parse_date(d: date | str) -> date:
    if isinstance(d, date):
        return d
    return date.fromisoformat(d)
