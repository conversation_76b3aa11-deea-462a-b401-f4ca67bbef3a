from __future__ import annotations

from typing import TYPE_CHECKING

import polars as pl
from sigrun_loader.scanner import parse_func

from sigrun.sigrun import py_infer_schema, py_infer_source

if TYPE_CHECKING:
    from datetime import date, time
    from typing import Callable

    from sigrun import Marker


def infer_schema(func: Callable, markers: list[Marker] | None = None) -> pl.Schema:
    """
    从Sigrun函数推断出一个 Schema。

    该函数应在不同的标记输入中返回相同的 Schema。

    参数:
        func: 一个 Sigrun 函数。
        markers: Sigrun Markers 对象的列表。

    返回:
        一个 Polars Schema。
    """
    py_schema = py_infer_schema(parse_func(func), markers)

    return pl.Schema(py_schema)


def infer_virgo_table_sources(
    func: Callable,
    date: str | date | None = None,
    time: str | time | None = None,
    markers: list[Marker] | None = None,
    filter_high_freq: bool = True,
) -> list[tuple[str, str]]:
    """
    从Sigrun函数推断出一个 Sources 列表。

    参数:
        func: 一个Sigrun函数。
        date: 用于推断来源的日期。
        time: 用于推断来源的时间。
        markers: Sigrun Markers 对象的列表。

    返回:
        一个包含表名和 Json 格式参数的列表。
    """

    return py_infer_source(parse_func(func), filter_high_freq, date, time, markers)
