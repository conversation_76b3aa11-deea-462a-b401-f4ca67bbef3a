from __future__ import annotations

from enum import Enum
from typing import TYPE_CHECKING, Literal, Union

if TYPE_CHECKING:
    import sys

    if sys.version_info >= (3, 10):
        from typing import TypeAlias
    else:
        from typing_extensions import TypeAlias


Location: TypeAlias = Literal["front", "back"]
Symbols: TypeAlias = Union[list[str], str]
Freqs: TypeAlias = Literal["1m", "5m", "10m", "15m", "30m", "60m", "1d"]

SINK_TYPE_KEY = "__sigrun_sink_type__"


class SinkType(Enum):
    VIRGO = "virgo"
    SAILOR = "sailor"
    TEST = "test"
