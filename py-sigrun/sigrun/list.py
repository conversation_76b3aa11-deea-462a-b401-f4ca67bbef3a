from __future__ import annotations

from typing import TYPE_CHECKING

from sigrun_loader.scanner import scan

if TYPE_CHECKING:
    from types import ModuleType
    from typing import Callable, List


def list(module: ModuleType) -> List[tuple[str, Callable]]:
    """
    List all sigrun functions in given module.

    Args:
        module (ModuleType): Python module to list sigrun functions from.

    Returns:
        List[str, Callable]: List of sigrun function names and instances.
    """
    funcs = (
        (f"{item['module']}.{item['name']}", item["func"])
        for item in scan(module.__name__)
    )
    return sorted(funcs, key=lambda x: x[0])
