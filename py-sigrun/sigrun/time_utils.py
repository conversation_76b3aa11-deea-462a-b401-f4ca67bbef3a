from __future__ import annotations

from typing import TYPE_CHECKING

from sigrun.sigrun import py_to_timedelta, py_time_before

if TYPE_CHECKING:
    from datetime import time, timedelta


def to_timedelta(input: str | time | int) -> timedelta:
    """
    将时间字符串或 time 对象转换为 timedelta 对象。

    参数:
        input: 可以是格式为"HH:MM:SS"的字符串或time对象

    返回:
        timedelta: 从00:00:00到输入时间的时间差

    示例:
        >>> to_timedelta("09:31:00")
        datetime.timedelta(hours=9, minutes=31)
        >>> to_timedelta(datetime.time(9, 31, 0))
        datetime.timedelta(hours=9, minutes=31)
    """
    return py_to_timedelta(input)


def time_before(
    base: str | time, seconds: int = 0, minutes: int = 0, hours: int = 0
) -> time:
    """
    计算在指定时间之前的时间，并自动绕过非交易时间。overflow 时会 panic

    参数:
        base: 可以是格式为"HH:MM:SS"的字符串或time对象
        seconds: 秒数
        minutes: 分钟数
        hours: 小时数

    返回:
        time: 在指定时间之前的时间

    示例:
        >>> time_before("13:00:00", minutes=30)
        datetime.time(11, 0, 0)

        >>> time_before(datetime.time(13, 0, 0), minutes=30)
        datetime.time(11, 0, 0)

        >>> time_before(datetime.time(11, 0, 0), hours=12)
        >>> panic: overflow
    """
    return py_time_before(base, seconds, minutes, hours)
