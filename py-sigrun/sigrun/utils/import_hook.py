from importlib.abc import Loader, MetaPathFinder
from importlib.machinery import ModuleSpec


class MyMetaPathFinder(MetaPathFinder):
    def find_spec(self, fullname, path, target=None):
        if fullname == "polars.polars":
            return ModuleSpec(fullname, MyLoader())
        return None


class MyLoader(Loader):
    def create_module(self, spec):
        from sys import modules

        # bin 会将 plr 以 patch-polars 的名字放入 sys.modules 中
        patch = modules.get("patch-polars")
        if patch is not None:
            return patch
        else:
            from sigrun.sigrun import polars as plr

            return plr

    def exec_module(self, module):
        pass
