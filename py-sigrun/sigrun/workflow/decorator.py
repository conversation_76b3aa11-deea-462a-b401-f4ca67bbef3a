from __future__ import annotations

import inspect
from functools import update_wrapper, wraps
from typing import Callable

import polars as pl

from sigrun.classes.marker import Marker
from sigrun.workflow.sink import Sailor


def stream(prefix: str | Callable):
    return _build_decorator(prefix, _stream_wrapper)


def factor(prefix: str | Callable):
    return _build_decorator(prefix, _factor_wrapper)


def _build_decorator(prefix: str | Callable, wrapper_fn: Callable):
    if callable(prefix):
        wrapper = wrapper_fn(prefix, None)
        return update_wrapper(wrapper, prefix)

    if isinstance(prefix, str):
        prefix_str = prefix

    def decorating_function(user_function):
        wrapper = wrapper_fn(user_function, prefix_str)
        return update_wrapper(wrapper, user_function)

    return decorating_function


def _stream_wrapper(func, prefix: str | None):
    def wrapper(*args, **kwargs):
        ret = func(*args, **kwargs)

        if isinstance(prefix, str):
            schema = ret.collect_schema()
            rename_exprs = [
                pl.col(name).alias(f"{prefix}.{name}")
                if dtype == pl.Float64 or dtype == pl.Float32
                else pl.col(name)
                for name, dtype in schema.items()
            ]
            ret = ret.select(rename_exprs)

        return Sailor(is_temp=True).sink(ret)

    setattr(wrapper, "__sigrun__", True)

    return wrapper


def _factor_wrapper(func, prefix: str | None):
    def wrapper(*args, **kwargs):
        signature = inspect.signature(func).parameters

        date, time = None, None
        for index, key in enumerate(signature):
            if key == "date":
                date = args[index]
            elif key == "time":
                time = args[index]

        if date is None or time is None:
            raise ValueError("factor function must have date and time parameter")

        ret = func(*args, **kwargs)

        if isinstance(prefix, str):
            schema = ret.collect_schema()
            rename_exprs = [
                pl.col(name).alias(f"{prefix}.{name}")
                if dtype == pl.Float64 or dtype == pl.Float32
                else pl.col(name)
                for name, dtype in schema.items()
            ]
            ret = ret.select(rename_exprs)

        ret = ret.with_columns(date=pl.lit(date), time=pl.lit(time))

        return Sailor(is_temp=True).sink(ret)

    setattr(wrapper, "__sigrun__", True)

    return wrapper


def task(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        return func(*args, **kwargs)

    setattr(wrapper, "__sigrun__", True)

    return wrapper


def parametrize(name: str, value: list):
    def _decorator(func):
        @wraps(func)
        def wrapper(*args, **func_kwargs):
            return func(*args, **func_kwargs)

        markers = getattr(wrapper, "__sigrun_markers__", [])

        markers.append(Marker(name, value))

        setattr(wrapper, "__sigrun_markers__", markers)

        return wrapper

    return _decorator
