from __future__ import annotations

from typing import TYPE_CHECKING

from ..type_aliases import SINK_TYPE_KEY, SinkType

if TYPE_CHECKING:
    from polars import LazyFrame


class Virgo:
    table: str
    options: dict

    def __init__(self, table: str, **kwargs) -> None:
        self.table = table
        self.options = kwargs

    def sink(self, ldf: LazyFrame) -> LazyFrame:
        setattr(ldf, SINK_TYPE_KEY, SinkType.VIRGO.value)
        setattr(ldf, "virgo", self)

        return ldf


class Sailor:
    is_temp: bool

    def __init__(self, is_temp: bool = False) -> None:
        self.is_temp = is_temp

    def sink(self, ldf):
        setattr(ldf, SINK_TYPE_KEY, SinkType.SAILOR.value)
        setattr(ldf, "sailor", self)
        return ldf


class Test:
    print: bool

    def __init__(self, print_: bool = False) -> None:
        self.print = print_

    def sink(self, ldf: LazyFrame) -> LazyFrame:
        setattr(ldf, SINK_TYPE_KEY, SinkType.TEST.value)
        setattr(ldf, "test", self)
        return ldf
