from typing import List
from datetime import time


class Schedule:
    def __init__(self, tag: int, val=None):
        self.tag = tag
        self.val = val

    @staticmethod
    def factor():
        return Schedule(0)

    @staticmethod
    def time(times: List[time]):
        return Schedule(1, times)

    @staticmethod
    def cron(cron_expression: str):
        return Schedule(2, cron_expression)
