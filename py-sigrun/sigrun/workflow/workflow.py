from __future__ import annotations

from polars import LazyFrame

from .sink import Virgo


class Workflow:
    _sink: Virgo
    _ldf: <PERSON><PERSON><PERSON>ram<PERSON>

    def __init__(self, sink: Virgo, ldf: LazyFrame) -> None:
        self._sink = sink
        self._ldf = ldf

    @classmethod
    def sink_virgo(cls, ldf: LazyFrame, table: str, **kwargs) -> Workflow:
        virgo = Virgo(table, **kwargs)

        return cls(virgo, ldf)
