import logging
import os


class SigrunIo:
    reset = True

    def __enter__(self):
        if "SIGRUN_IO_ENGINE" not in os.environ:
            os.environ["SIGRUN_IO_ENGINE"] = "true"
        else:
            if os.environ["SIGRUN_IO_ENGINE"] != "true":
                logging.warning("SIGRUN_IO_ENGINE disabled, do not use in production.")

            self.reset = False

    def __exit__(self, *args):
        if self.reset:
            del os.environ["SIGRUN_IO_ENGINE"]
