from __future__ import annotations

from typing import TYPE_CHECKING

from sigrun.utils.plugin_finder import plugin_path

from ._parse_expr import parse_into_expr

if TYPE_CHECKING:
    import polars as pl
    from polars.type_aliases import IntoExpr

    from sigrun.type_aliases import Location


def sample(
    expr: IntoExpr, *, n: int, offset: int = 0, location: Location = "front"
) -> pl.Expr:
    expr = parse_into_expr(expr)
    return expr.register_plugin(
        lib=plugin_path(),
        symbol="sample",
        is_elementwise=True,
        kwargs={"n": n, "offset": offset, "location": location},
    )
