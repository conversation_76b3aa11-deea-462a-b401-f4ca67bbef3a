from __future__ import annotations

from typing import TYPE_CHECKING

from sigrun.sigrun import PyDataType, PySecurityType, py_high_freq
from sigrun.virgo.stream.windows import WantWindows

if TYPE_CHECKING:
    from sigrun.type_aliases import Freqs


def snapshots() -> WantWindows:
    return WantWindows(
        py_high_freq(
            PySecurityType.index(),
            PyDataType.snapshot(),
        )
    )


def bars(
    freq: Freqs,
) -> WantWindows:
    return WantWindows(
        py_high_freq(
            PySecurityType.index(),
            PyDataType.bar(freq),
        )
    )
