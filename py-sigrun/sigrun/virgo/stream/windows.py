from __future__ import annotations

from typing import TYPE_CHECKING

import polars as pl

from sigrun.sigrun import PyWindowsType
from sigrun.sigrun import (
    WantWindows as RsWantWindows,
)

if TYPE_CHECKING:
    from datetime import time, timedelta


def tumbling(
    width: timedelta,
    earliest: time | str | None = None,
    latest: time | str | None = None,
) -> PyWindowsType:
    return PyWindowsType.tumbling(width, earliest, latest)


def growing(
    width: timedelta,
    earliest: time | str | None = None,
    latest: time | str | None = None,
) -> PyWindowsType:
    return PyWindowsType.growing(width, earliest, latest)


class WantWindows:
    inner: RsWantWindows

    def __init__(self, inner: RsWantWindows):
        self.inner = inner

    def window(self, windows_type: PyWindowsType) -> pl.LazyFrame:
        lf = pl.LazyFrame.__new__(pl.LazyFrame)

        lf._ldf = self.inner.windows(windows_type)

        return lf
