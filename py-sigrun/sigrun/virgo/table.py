from __future__ import annotations

from typing import TYPE_CHECKING
import warnings

import polars as pl

from sigrun.context import python_context, rust_context

if TYPE_CHECKING:
    import datetime


def init(secret_name: str, secret_value: str):
    ctx = rust_context()
    ctx.set_virgo_secret(secret_name, secret_value)


def read(
    table: str, timeout: datetime.timedelta | None = None, **options
) -> pl.LazyFrame:
    warnings.warn(
        "The `read` function is deprecated and will be removed in a future release."
        " Use the `scan` function instead.",
        DeprecationWarning,
    )
    return scan(table, timeout, **options)


def scan(
    table: str, timeout: datetime.timedelta | None = None, **options
) -> pl.LazyFrame:
    return python_context().get_virgo_client().scan(table, timeout, **options)
