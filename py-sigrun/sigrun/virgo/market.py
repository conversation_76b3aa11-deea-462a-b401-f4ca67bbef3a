from __future__ import annotations

import datetime
from typing import TYPE_CHECKING

from polars._utils.parse import parse_into_expression
from polars._utils.wrap import wrap_expr

from sigrun.sigrun import PyTradingDays

if TYPE_CHECKING:
    from datetime import date

    from polars import Expr
    from polars._typing import (
        IntoExpr,
        Roll,
    )


class _TradingDays:
    _instance = None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = PyTradingDays()

        return cls._instance


def add_trading_days(input: IntoExpr, n: int | IntoExpr, roll: Roll = "raise") -> Expr:
    """
    将 input 中的日期列向前或向后移动 n 个交易日。

    Args:
        input: 输入的日期列。
        n: 要偏移的交易日数量。可以是单个数字或表达式。
        roll:
            当开始日期落在非交易日时的处理方式。选项有：
            - `'raise'`: 抛出错误
            - `'forward'`: 移动到下一个交易日
            - `'backward'`: 移动到上一个交易日

    Returns:
        返回一个新的表达式, 数据类型保持不变。
    """
    return wrap_expr(
        _TradingDays.get_instance().add_trading_day(
            parse_into_expression(input),
            parse_into_expression(n),
            roll,
        )
    )


def trading_days(from_date: str | date, to_date: str | date) -> list:
    """
    获得交易日字符串列表，最晚的交易日不会超过今天。

    Args:
        from_date: 开始日期，示例：'2007-01-01'
        to_date: 结束日期，示例：'2007-01-01'

    Returns:
        返回交易日列表, 示例：['2007-01-01','2007-01-2'...]
    """
    return _TradingDays.get_instance().trading_days(from_date, to_date)


def trading_day(span: int = 0, date: str | date | None = None) -> date | None:
    """
    获取前/后第N个交易日

    Args:
        span: 跨度。默认为1。大于0表示向后，小于0表示向前。
            等于0且指定日期为不为交易日时返回 None
        date: 指定的交易日
    """
    date = date or today()

    return _TradingDays.get_instance().trading_day(date, span)


def is_trading_day(date: str | date | None = None) -> bool:
    """
    检验是否是交易日。

    Args:
        date: 交易日期，示例：'2007-01-01'
    Returns:
        True: 是交易日。
    """
    date = date or today()

    return _TradingDays.get_instance().is_trading_day(date)


def today() -> date:
    return datetime.datetime.now().date()
