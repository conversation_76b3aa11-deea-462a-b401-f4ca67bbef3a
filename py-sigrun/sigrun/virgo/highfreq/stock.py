from __future__ import annotations

from typing import TYPE_CHECKING

from sigrun.sigrun import PyDataType, PySecurityType

from .utils import read_high_freq_helper

if TYPE_CHECKING:
    from datetime import date, time

    import polars as pl

    from sigrun.type_aliases import Freqs, Symbols


def matches(
    symbols: Symbols,
    from_date: str | date,
    to_date: str | date | None = None,
    from_time: str | time | None = None,
    to_time: str | time | None = None,
    force_columns: list[str] | None = None,
) -> pl.LazyFrame:
    """
    获取股票的逐笔成交数据

    Args:
        symbols: 需要获取的证券代码，可以是:
            - 'ALL' : 获取所有的A股数据
            - 股票交易所代码: 'SSE', 'SZE'
            - 证券代码列表: 示例: ['000001.SZE', '000002.SZE', '600000.SSE', ...]
            - 单个证券代码: 示例: '000001.SZE'
        from_date: 起始日期。示例：'2007-01-01'。
        to_date: 结束日期。示例：'2007-01-01'。
        from_time: 起始时间，设置该值时，每日的数据都从这个时间点开始。示例: '10:00:00'。
        to_time: 结束时间，设置该值时，每日的数据都在这个时间点结束。示例: '10:00:00'。
        force_columns: 强制返回的列。
    Returns:
        LazyFrame
    """
    return read_high_freq_helper(
        PySecurityType.stock(),
        PyDataType.match(),
        symbols,
        from_date,
        to_date,
        from_time,
        to_time,
        force_columns,
    )


def orders(
    symbols: Symbols,
    from_date: str | date,
    to_date: str | date | None = None,
    from_time: str | time | None = None,
    to_time: str | time | None = None,
    force_columns: list[str] | None = None,
) -> pl.LazyFrame:
    """
    获取股票的逐笔委托数据

    Args:
        symbols: 需要获取的证券代码，可以是:
            - 'ALL' : 获取所有的A股数据
            - 股票交易所代码: 'SSE', 'SZE'
            - 证券代码列表: 示例: ['000001.SZE', '000002.SZE', '600000.SSE', ...]
            - 单个证券代码: 示例: '000001.SZE'
        from_date: 起始日期。示例：'2007-01-01'。
        to_date: 结束日期。示例：'2007-01-01'。
        from_time: 起始时间，设置该值时，每日的数据都从这个时间点开始。示例: '10:00:00'。
        to_time: 结束时间，设置该值时，每日的数据都在这个时间点结束。示例: '10:00:00'。
        force_columns: 强制返回的列。
    Returns:
        LazyFrame
    """
    return read_high_freq_helper(
        PySecurityType.stock(),
        PyDataType.order(),
        symbols,
        from_date,
        to_date,
        from_time,
        to_time,
        force_columns,
    )


def snapshots(
    symbols: Symbols,
    from_date: str | date,
    to_date: str | date | None = None,
    from_time: str | time | None = None,
    to_time: str | time | None = None,
    force_columns: list[str] | None = None,
) -> pl.LazyFrame:
    """
    获取股票的快照数据

    Args:
        symbols: 需要获取的证券代码，可以是:
            - 'ALL' : 获取所有的A股数据
            - 股票交易所代码: 'SSE', 'SZE'
            - 证券代码列表: 示例: ['000001.SZE', '000002.SZE', '600000.SSE', ...]
            - 单个证券代码: 示例: '000001.SZE'
        from_date: 起始日期。示例：'2007-01-01'。
        to_date: 结束日期。示例：'2007-01-01'。
        from_time: 起始时间，设置该值时，每日的数据都从这个时间点开始。示例: '10:00:00'。
        to_time: 结束时间，设置该值时，每日的数据都在这个时间点结束。示例: '10:00:00'。
        force_columns: 强制返回的列。

    Returns:
        LazyFrame
    """
    return read_high_freq_helper(
        PySecurityType.stock(),
        PyDataType.snapshot(),
        symbols,
        from_date,
        to_date,
        from_time,
        to_time,
        force_columns,
    )


def bars(
    symbols: Symbols,
    freq: Freqs,
    from_date: str | date,
    to_date: str | date | None = None,
    from_time: str | time | None = None,
    to_time: str | time | None = None,
    force_columns: list[str] | None = None,
) -> pl.LazyFrame:
    """
    获取股票的K线数据

    Args:
        symbols: 需要获取的证券代码，可以是:
            - 'ALL' : 获取所有的A股数据
            - 股票交易所代码: 'SSE', 'SZE'
            - 证券代码列表: 示例: ['000001.SZE', '000002.SZE', '600000.SSE', ...]
            - 单个证券代码: 示例: '000001.SZE'
        freq: K线的频率。
        from_date: 起始日期。示例：'2007-01-01'。
        to_date: 结束日期。示例：'2007-01-01'。
        from_time: 起始时间，设置该值时，每日的数据都从这个时间点开始。示例: '10:00:00'。
        to_time: 结束时间，设置该值时，每日的数据都在这个时间点结束。示例: '10:00:00'。
        force_columns: 强制返回的列。

    Returns:
        LazyFrame
    """
    return read_high_freq_helper(
        PySecurityType.stock(),
        PyDataType.bar(freq),
        symbols,
        from_date,
        to_date,
        from_time,
        to_time,
        force_columns,
    )
