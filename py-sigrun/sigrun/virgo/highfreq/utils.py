from __future__ import annotations

from typing import TYPE_CHECKING

import polars as pl

from sigrun.context import python_context
from sigrun.sigrun import PyHighFreqReader

from ..market import _TradingDays

if TYPE_CHECKING:
    from datetime import date, time

    from sigrun.sigrun import PyDataType, PySecurityType
    from sigrun.type_aliases import Symbols


class _HighFreqReader:
    _instance = None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = PyHighFreqReader(_TradingDays.get_instance())

        return cls._instance


def read_high_freq_helper(
    secu_type: PySecurityType,
    data_type: PyDataType,
    symbols: Symbols,
    from_date: str | date,
    to_date: str | date | None = None,
    from_time: str | time | None = None,
    to_time: str | time | None = None,
    force_columns: list[str] | None = None,
) -> pl.LazyFrame:
    lf = pl.LazyFrame.__new__(pl.LazyFrame)

    lf._ldf = _HighFreqReader.get_instance().read_high_freq_helper(
        secu_type,
        data_type,
        symbols,
        from_date,
        python_context().get_virgo_client()._inner,
        to_date,
        from_time,
        to_time,
        force_columns,
    )

    return lf
