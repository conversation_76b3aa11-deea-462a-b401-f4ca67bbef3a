from __future__ import annotations

import os
from typing import TYPE_CHECKING, Iterable

import polars as pl
from polars._utils.parse import (
    parse_into_list_of_expressions,
)

from sigrun.sigrun import py_with_columns_expr

if TYPE_CHECKING:
    from polars.type_aliases import IntoExpr


def with_columns_expr(
    self: pl.<PERSON>zy<PERSON>rame,
    *exprs: IntoExpr | Iterable[IntoExpr],
    **named_exprs: IntoExpr,
) -> pl.LazyFrame:
    structify = bool(int(os.environ.get("POLARS_AUTO_STRUCTIFY", 0)))

    pyexprs = parse_into_list_of_expressions(
        *exprs, **named_exprs, __structify=structify
    )
    return pl.LazyFrame._from_pyldf(py_with_columns_expr(self._ldf, pyexprs))
