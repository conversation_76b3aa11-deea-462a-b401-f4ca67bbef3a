import sys

from sigrun.utils import MyMetaPathFinder

sys.meta_path.insert(0, MyMetaPathFinder())

from sigrun import polars, utils, virgo, workflow  # noqa: E402
from sigrun.classes import Marker  # noqa: E402
from sigrun.list import list  # noqa: E402
from sigrun.run import (  # noqa: E402
    failed,
    fill_null,
    ignore,
    run,
    run_all,
    run_all_with_dates,
    run_with_dates,
    run_stream,
)
from sigrun.schema import infer_schema, infer_virgo_table_sources  # noqa: E402
from sigrun.wait import wait_until_ready  # noqa: E402
from sigrun.time_utils import to_timedelta, time_before  # noqa: E402

utils.register_plugin(__file__)


def is_factor(func) -> bool:
    return getattr(func, "__sigrun__", False)


__all__ = [
    "virgo",
    "polars",
    "workflow",
    "run",
    "run_all",
    "run_all_with_dates",
    "run_with_dates",
    "run_stream",
    "wait_until_ready",
    "failed",
    "fill_null",
    "ignore",
    "list",
    "infer_schema",
    "infer_virgo_table_sources",
    "Marker",
    "is_factor",
    "to_timedelta",
    "time_before",
]
