[package]
name = "py-sigrun"
version.workspace = true
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[lib]
name = "sigrun"
crate-type = ["cdylib", "rlib"]

[dependencies]
anyhow = "1"
pyo3 = { workspace = true, features = ["anyhow", "chrono"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
chrono = { version = "0.4.19" }
tracing-subscriber = { version = "0.3.18", features = [
    "env-filter",
    "time",
    "chrono",
] }
tracing = "0.1.40"
time = "0.3.17"
tokio = { version = "1.32.0", features = ["rt", "rt-multi-thread"] }
rayon = "1"
rustls = "0.23"
console = { version = "0.16", default-features = false, features = [
    "ansi-parsing",
    "std",
] }
indicatif = "0.17"
enum_dispatch = "0.3"
reqwest = { version = "0.12", features = ["json"] }
backon = "1"
tracing-opentelemetry = "0.31"
opentelemetry = "0.30"
tqdm-rs = { git = "https://gitlab.sci-inv.cn/rustlib/tqdm-rs.git" }
pyo3-otel = { git = "https://gitlab.sci-inv.cn/rustlib/pyo3-otel.git" }
alert-client = { git = "https://gitlab.sci-inv.cn/rustlib/alert-libs.git", package = "alert-client", features = [
    "otel",
] }


dyconfig.workspace = true
polars-python = { workspace = true, features = ["full", "nightly"] }
polars = { workspace = true, features = ["dtype-full", "temporal"] }
polars-core = { workspace = true }
pypolars = { workspace = true, features = ["full", "nightly"] }

virgo = { workspace = true, features = ["table", "highfreq", "market"] }
sigrun-loader.workspace = true
pyo3-utils.workspace = true
datetime-iterator.workspace = true


polars-exts = { path = "../crates/polars-exts" }
sigrun-virgo = { path = "../crates/sigrun-virgo" }
sigrun-context = { path = "../crates/sigrun-context" }
sigrun-io-expr = { path = "../crates/sigrun-io-expr" }
sigrun-engine = { path = "../crates/sigrun-engine" }
runtime = { path = "../crates/runtime" }
