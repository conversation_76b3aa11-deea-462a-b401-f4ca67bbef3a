# x84_64 manylinux image
# for intel and amd
FROM quay.io/pypa/manylinux_2_28_x86_64

# install rust and build tools
RUN curl --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y \
    && echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> ~/.bashrc \
    && source ~/.bashrc \
    && rustup target add x86_64-unknown-linux-musl \
    && yum install -y pkg-config openssl openssl-devel wget 

# add cargo to ENV
ENV PATH=/root/.cargo/bin:$PATH

ADD rust-toolchain.toml /sigrun-sdk/rust-toolchain.toml
# automatically install toolchain
RUN cd sigrun-sdk && rustc --version \
    # && cargo install --locked maturin \
    && cargo install cargo-make sd cargo-nextest \
    && cargo install --git https://gitlab.sci-inv.cn/rustlib/cargo-config.git


# install conda for test 
RUN wget --quiet https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh -O /tmp/miniconda.sh \
    && /bin/bash /tmp/miniconda.sh -b -p /opt/conda \
    && rm /tmp/miniconda.sh \
    && /opt/conda/bin/conda clean -tipy \ 
    && /opt/conda/bin/conda create -n cli python=3.10 -y \
    && /opt/conda/bin/conda init && source ~/.bashrc 

ENV PATH=/opt/conda/bin:$PATH
ENV PIP_EXTRA_INDEX_URL=http://pypi.sci-inv.cn PIP_TRUSTED_HOST=pypi.sci-inv.cn

RUN echo $PATH
SHELL ["conda", "run", "-n", "cli", "/bin/bash", "-c"]

RUN pip install maturin pytest

# TODO: Rust compile build dependencies cache

ADD . /sigrun-sdk

RUN cargo --version
RUN cd /sigrun-sdk/py-sigrun \
    && maturin develop  \
    && pytest \
    && cargo make nextest 

CMD ["/bin/bash"]
