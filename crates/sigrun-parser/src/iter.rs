use std::collections::VecDeque;

use polars_plan::{
    plans::{IR, IRPlan},
    prelude::Node,
};

pub fn node_iter(plan: &IRPlan) -> NodeIter<'_> {
    NodeIter {
        queue: VecDeque::from([plan.lp_top]),
        plan,
    }
}

pub struct NodeIter<'a> {
    queue: VecDeque<Node>,
    plan: &'a IRPlan,
}

impl<'a> NodeIter<'a> {
    pub fn zip_node(self) -> ZipNodeIter<'a> {
        ZipNodeIter {
            queue: self.queue,
            plan: self.plan,
        }
    }
}

pub struct ZipNodeIter<'a> {
    queue: VecDeque<Node>,
    plan: &'a IRPlan,
}

impl<'a> Iterator for ZipNodeIter<'a> {
    type Item = (Node, &'a IR);

    fn next(&mut self) -> Option<Self::Item> {
        let node = self.queue.pop_front()?;

        let ret_ir = unsafe { self.plan.lp_arena.get_unchecked(node) };

        ret_ir.copy_inputs(&mut self.queue);

        Some((node, ret_ir))
    }
}

impl<'a> Iterator for NodeIter<'a> {
    type Item = &'a IR;

    fn next(&mut self) -> Option<Self::Item> {
        let node = self.queue.pop_front()?;

        let ret_ir = unsafe { self.plan.lp_arena.get_unchecked(node) };

        ret_ir.copy_inputs(&mut self.queue);

        Some(ret_ir)
    }
}
