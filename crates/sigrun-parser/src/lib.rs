use std::sync::Arc;

use polars::prelude::{AnonymousScan, FileScanIR};
use polars_plan::{
    plans::{IR, IRPlan},
    prelude::Node,
};

pub mod iter;
pub mod scan_opt;

// 不同 filter 和 projection 下，相同的 io 由于 AnonymousScan 强制在 Arc 中，所以指向了同一片内存。
// 所以这里需要替换掉 Injector，使其指向不同的内存，避免数据错乱。
pub fn rewrite_scan<T>(plan: &mut IRPlan, node: Node)
where
    T: AnonymousScan + Clone + 'static,
{
    let ir = unsafe { plan.lp_arena.get_unchecked(node) };
    let inputs = ir.get_inputs();
    if inputs.is_empty() {
        return;
    }

    for input_node in inputs.iter() {
        let input_expr = plan.lp_arena.get_mut(*input_node);
        if let IR::Scan { scan_type, .. } = input_expr {
            let function = match &mut **scan_type {
                FileScanIR::Anonymous { function, .. } => function,
                _ => continue,
            };

            if let Some(injector) = function.as_any().downcast_ref::<T>() {
                *function = Arc::new(injector.clone());
            }
        } else {
            rewrite_scan::<T>(plan, *input_node);
        }
    }
}
