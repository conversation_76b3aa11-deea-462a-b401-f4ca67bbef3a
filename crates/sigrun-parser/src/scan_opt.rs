use std::sync::Arc;

use polars::prelude::{AnonymousScan, FileScanIR, PlSmallStr};
use polars_plan::plans::{IR, expr_ir::ExprIR};

pub type Scan = Arc<dyn AnonymousScan>;
pub type Proj = Arc<[PlSmallStr]>;

pub struct ScanOpt<'a, T = Scan> {
    pub scan: &'a T,
    pub proj: Option<&'a Proj>,
    pub predicate: &'a Option<ExprIR>,
}

impl<'a, T> ScanOpt<'a, T> {
    pub fn filter_map<U>(self, f: impl FnOnce(&T) -> Option<&U>) -> Option<ScanOpt<'a, U>> {
        f(self.scan).map(|u| ScanOpt {
            scan: u,
            proj: self.proj,
            predicate: self.predicate,
        })
    }
}

pub fn extract_scan(ir: &IR) -> Option<&Scan> {
    if let IR::Scan { scan_type, .. } = ir {
        if let FileScanIR::Anonymous { function, .. } = &**scan_type {
            return Some(function);
        }
    }

    None
}

pub fn extract_scan_opt(ir: &IR) -> Option<ScanOpt<'_>> {
    if let IR::Scan {
        scan_type,
        unified_scan_args,
        predicate,
        ..
    } = ir
    {
        if let FileScanIR::Anonymous { function, .. } = &**scan_type {
            let proj = unified_scan_args.projection.as_ref();
            return Some(ScanOpt {
                scan: function,
                proj,
                predicate,
            });
        }
    }

    None
}
