use polars::prelude::PlSmallStr;
use serde::{Serialize, ser::SerializeMap};
use serde_json::Value;

pub struct Override<'a> {
    v: &'a serde_json::Map<String, Value>,
    columns: &'a [PlSmallStr],
}

impl<'a> Override<'a> {
    pub fn new(v: &'a serde_json::Map<String, Value>, columns: &'a [PlSmallStr]) -> Self {
        Self { v, columns }
    }
}

impl Serialize for Override<'_> {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        let mut map = serializer.serialize_map(Some(self.v.len() + 1))?;
        for (k, v) in self.v.iter().filter(|(k, _)| *k != "columns") {
            map.serialize_entry(k, v)?;
        }

        map.serialize_key("columns")?;
        map.serialize_value(self.columns)?;

        map.end()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_override_serialization() {
        let mut map = serde_json::Map::new();
        map.insert("name".to_string(), json!("test"));
        map.insert("columns".to_string(), json!(["col3", "col4"]));

        // 验证序列化结果
        let serialized = test_override_common(
            &map,
            &vec![PlSmallStr::from("col1"), PlSmallStr::from("col2")],
        );
        let obj = serialized.as_object().unwrap();

        // 特定于此测试的断言
        assert_eq!(obj.get("name").unwrap(), "test");
    }

    #[test]
    fn test_override_empty() {
        // 创建空的测试数据
        let map = serde_json::Map::new();
        // 验证序列化结果
        test_override_common(
            &map,
            &vec![PlSmallStr::from("col1"), PlSmallStr::from("col2")],
        );
    }

    #[test]
    fn test_columns_not_exist() {
        // 创建没有columns字段的测试数据
        let mut map = serde_json::Map::new();
        map.insert("name".to_string(), json!("test"));

        // 验证序列化结果
        test_override_common(
            &map,
            &vec![PlSmallStr::from("col1"), PlSmallStr::from("col2")],
        );
    }

    fn test_override_common(map: &serde_json::Map<String, Value>, columns: &[PlSmallStr]) -> Value {
        let override_obj = Override { v: map, columns };

        // 序列化对象
        let serialized = serde_json::to_value(&override_obj).unwrap();

        // 验证结果
        assert!(serialized.is_object());
        let obj = serialized.as_object().unwrap();

        // 验证columns字段
        let columns_array = obj.get("columns").unwrap().as_array().unwrap();
        assert_eq!(columns_array.len(), 2);
        assert_eq!(columns_array[0], columns[0].as_str());
        assert_eq!(columns_array[1], columns[1].as_str());

        // 验证字符串序列化
        let s = serde_json::to_string(&override_obj).unwrap();
        println!("{}", s);
        assert_eq!(serde_json::from_str::<Value>(&s).unwrap(), serialized);

        serialized
    }
}
