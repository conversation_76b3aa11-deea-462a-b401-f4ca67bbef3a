use polars::prelude::*;

pub fn cast_df(df: DataFrame, schema: &SchemaRef) -> PolarsResult<DataFrame> {
    let height = df.height();

    let cols = df
        .take_columns()
        .into_iter()
        .map(|col| {
            let name = col.name();
            let target_dtype = schema
                .get(name.as_str())
                .ok_or_else(|| PolarsError::ColumnNotFound(name.to_string().into()))?;
            col.cast(target_dtype)
        })
        .collect::<Result<Vec<_>, PolarsError>>()?;

    Ok(unsafe { DataFrame::new_no_checks(height, cols) })
}
