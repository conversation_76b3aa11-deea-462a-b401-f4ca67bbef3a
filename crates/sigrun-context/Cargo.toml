[package]
name = "sigrun-context"
edition = "2021"
version.workspace = true

[dependencies]
pyo3 = { workspace = true }
base64 = "0.22"
sigrun-virgo = { path = "../sigrun-virgo" }
virgo = { workspace = true, features = ["table"] }
runtime = { path = "../runtime" }


[dev-dependencies]
pyo3 = { workspace = true, features = ["auto-initialize"] }
tokio = { version = "1.32.0", features = ["rt", "rt-multi-thread", "macros"] }
