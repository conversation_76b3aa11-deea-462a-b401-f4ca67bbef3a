use pyo3::prelude::*;

use crate::python_context::encode_token;

pub fn get_rust_context(py: Python<'_>) -> PyResult<RustContext> {
    PyModule::import(py, "sigrun.context")?
        .getattr("rust_context")?
        .call0()?
        .extract()
}

#[pyclass]
#[derive(<PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct RustContext {
    token: String,
}

impl RustContext {
    #[inline]
    pub fn initialized(&self) -> bool {
        !self.token.is_empty()
    }

    pub fn token(&self) -> &str {
        &self.token
    }
}

#[pymethods]
impl RustContext {
    #[new]
    pub fn new() -> Self {
        Self::default()
    }

    pub fn set_virgo_secret(&mut self, secret_name: &str, secret_value: &str) {
        self.token = encode_token(secret_name, secret_value);
    }
}
