use crate::RustContext;
use base64::{engine::general_purpose, write::EncoderStringWriter};
use pyo3::exceptions::PyValueError;
use pyo3::prelude::*;
use runtime::current;
use sigrun_virgo::table::PyClient;
use std::io::Write;
use virgo::table::Virgo;

pub fn inject_python_context(py: Python<'_>, ctx: PythonContext) -> PyResult<()> {
    PyModule::import(py, "sigrun.context")?
        .getattr("inject_python_context")?
        .call1((ctx,))?;
    Ok(())
}

pub fn set_server_mode(py: Python<'_>) -> PyResult<()> {
    PyModule::import(py, "sigrun.context")?
        .getattr("set_server_mode")?
        .call0()?;
    Ok(())
}

pub fn reset_context(py: Python<'_>) -> PyResult<()> {
    PyModule::import(py, "sigrun.context")?
        .getattr("reset_context")?
        .call0()?;
    Ok(())
}

#[pyclass]
#[derive(Clone)]
pub struct PythonContext {
    virgo: PyClient,
}

impl PythonContext {
    pub fn virgo_rs(&self) -> &Virgo {
        &self.virgo.client
    }

    pub fn new(virgo: Virgo) -> Self {
        Self {
            virgo: PyClient { client: virgo },
        }
    }
}

#[pymethods]
impl PythonContext {
    // call in local mode
    #[new]
    pub fn from_rs_ctx(ctx: RustContext) -> PyResult<Self> {
        let runtime = current();
        let _guard = runtime.enter();
        if !ctx.initialized() {
            return Err(PyValueError::new_err("virgo not init"));
        }

        Ok(Self {
            virgo: PyClient {
                client: Virgo::new_with_token(
                    ctx.token()
                        .parse()
                        .map_err(|_| PyValueError::new_err("invalid token"))?,
                ),
            },
        })
    }

    pub fn get_virgo_client(&self) -> PyClient {
        self.virgo.clone()
    }
}

pub(crate) fn encode_token(sk_name: &str, sk_value: &str) -> String {
    let len = encode_length(sk_name.len() + sk_value.len() + 1);

    let mut container = String::with_capacity(len + "Basic ".len());
    container.push_str("Basic ");

    let mut enc = EncoderStringWriter::from_consumer(container, &general_purpose::URL_SAFE);
    enc.write_all(sk_name.as_bytes()).unwrap();
    enc.write_all(b":").unwrap();
    enc.write_all(sk_value.as_bytes()).unwrap();

    enc.into_inner()
}

fn encode_length(len: usize) -> usize {
    len.div_ceil(3) * 4
}

#[cfg(test)]
mod tests {
    use super::*;

    use std::io::Write;

    use base64::{engine::general_purpose, prelude::BASE64_URL_SAFE, Engine};
    use pyo3::ffi::c_str;

    #[test]
    fn encode_test() {
        let mut container = String::new();
        container.push_str("Basic ");

        let mut enc = base64::write::EncoderStringWriter::from_consumer(
            container,
            &general_purpose::URL_SAFE,
        );
        enc.write_all(b"hello").unwrap();
        enc.write_all(b":").unwrap();
        enc.write_all(b"world").unwrap();

        let t = enc.into_inner();

        let t1 = BASE64_URL_SAFE.encode("hello:world");

        println!("{t}, {t1}");
        assert_eq!(t, format!("Basic {t1}"));
    }

    #[tokio::test]
    async fn inject_test() {
        Python::with_gil(|py| {
            let ctx = PythonContext::new(Virgo::new("test", "test").unwrap());
            inject_python_context(py, ctx).unwrap();

            Python::run(
                py,
                c_str!(
                    r#"
from sigrun.context import python_context

ctx = python_context()

print(ctx)

assert ctx != None

                "#
                ),
                None,
                None,
            )
            .unwrap();
        });
    }
}
