[package]
name = "sigrun-virgo"
version.workspace = true
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
ahash = "0.8"
anyhow = "1"
pyo3 = { workspace = true, features = ["anyhow", "chrono"] }
pyo3-utils.workspace = true
serde_json = "1"
chrono = { version = "0.4.19" }
tokio = { version = "1.32.0", features = ["rt", "rt-multi-thread"] }
itertools = "0.14"
quick_cache = { version = "0.6", default-features = false }


polars-python = { workspace = true, features = ["full", "nightly"] }
polars = { workspace = true, features = ["dtype-full", "temporal", "lazy"] }
polars-arrow = { workspace = true }


virgo = { workspace = true, features = ["table", "highfreq", "market"] }
runtime = { path = "../runtime" }
sigrun-utils = { path = "../sigrun-utils" }
sigrun-io-expr = { path = "../sigrun-io-expr" }


[dev-dependencies]
polars = { workspace = true, features = ["lazy"] }
