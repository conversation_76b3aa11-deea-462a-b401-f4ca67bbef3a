use polars::prelude::*;
use runtime::current;
use serde_json::Value;
use virgo::table::Virgo;

use sigrun_utils::{convert::cast_df, r#override::Override};

#[derive(Clone)]
pub struct VirgoScan {
    virgo: Virgo,
    opt: serde_json::Map<String, Value>,
    schema: SchemaRef,
    table_name: String,
    timeout: Option<std::time::Duration>,
}

impl VirgoScan {
    pub fn new(
        virgo: Virgo,
        table_name: impl ToString,
        opt: serde_json::Map<String, Value>,
        schema: SchemaRef,
        timeout: Option<std::time::Duration>,
    ) -> Self {
        Self {
            virgo,
            table_name: table_name.to_string(),
            opt,
            schema,
            timeout,
        }
    }

    #[inline]
    pub fn table_name(&self) -> &str {
        &self.table_name
    }

    #[inline]
    pub fn opt(&self) -> &serde_json::Map<String, Value> {
        &self.opt
    }
}

impl AnonymousScan for VirgoScan {
    // fn eq(&self, other: &dyn AnonymousScan) -> bool {
    //     if let Some(other) = other.as_any().downcast_ref::<Self>() {
    //         self.table_name == other.table_name && self.opt == other.opt
    //     } else {
    //         false
    //     }
    // }

    fn allows_projection_pushdown(&self) -> bool {
        true
    }

    fn scan(&self, scan_opt: AnonymousScanArgs) -> PolarsResult<DataFrame> {
        let opt = if let Some(project) = scan_opt.with_columns {
            let o = Override::new(&self.opt, &project);

            serde_json::to_string(&o).unwrap()
        } else {
            serde_json::to_string(&self.opt).unwrap()
        };

        let df = current()
            .block_on(async {
                self.virgo
                    .read(&self.table_name, opt.as_str(), self.timeout)
                    .await
            })
            .map_err(|e| PolarsError::ComputeError(e.to_string().into()))?;

        cast_df(df, &self.schema)
    }

    fn schema(&self, _infer_schema_length: Option<usize>) -> PolarsResult<SchemaRef> {
        Ok(self.schema.clone())
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

#[cfg(test)]
mod tests {
    use serde_json::json;

    use super::*;

    fn setup() -> Virgo {
        std::env::set_var("VIRGO_DATA_SERVER_HOST", "d8.sci-inv.cn");
        std::env::set_var("VIRGO_DATA_SERVER_PORT", "18815");
        Virgo::new("virgo", std::env::var("VIRGO_SECRET").unwrap()).unwrap()
    }

    #[tokio::test]
    async fn test_get_schema() {
        let virgo = setup();
        let schema = virgo.get_schema("stock.basic_info", None).await.unwrap();

        let scan = VirgoScan::new(
            virgo,
            "stock.basic_info",
            json!({}).as_object().unwrap().clone(),
            schema.into(),
            None,
        );
        let schema = scan.schema(None).unwrap();
        println!("{:?}", schema);
        assert_eq!(schema.iter_fields().len(), 10);
    }

    #[tokio::test(flavor = "multi_thread")]
    async fn test_lazy_frame() {
        let virgo = setup();
        let schema = virgo.get_schema("stock.basic_info", None).await.unwrap();

        let scan = VirgoScan::new(
            virgo,
            "stock.basic_info",
            json!({
                "date": "2024-07-23"
            })
            .as_object()
            .unwrap()
            .clone(),
            schema.into(),
            None,
        );

        println!("{:?}", scan.schema);

        std::thread::scope(|s| {
            s.spawn(|| {
                let lf = LazyFrame::anonymous_scan(Arc::new(scan), ScanArgsAnonymous::default())
                    .unwrap();

                // let lf = lf.filter(col("start_time").gt(lit(Duration::parse("10h"))));
                let df = lf.collect().unwrap();
                println!("{:?}", df);
                assert_eq!(df.shape(), (5108, 10));
            });
        });
    }
}
