use std::{
    sync::{Arc, LazyLock},
    time::Duration,
};

use anyhow::bail;
use polars::prelude::*;
use polars_python::lazyframe::PyLazyFrame;
use pyo3::{exceptions::PyRuntimeError, prelude::*, types::PyDict};
use pyo3_utils::JsonDict;
use serde_json::Value;
use sigrun_io_expr::batch::{Injector, IoParam};
use sigrun_utils::env::io_engine_enabled;
use tokio::runtime::Handle;
use virgo::table::Virgo;

use runtime::current;

use crate::schema_cache::SchemaCache;

mod lazy;
pub use lazy::VirgoScan;

static SCHEMA_CACHE: LazyLock<SchemaCache> = LazyLock::new(|| SchemaCache::new(1024));

#[pyclass]
#[repr(transparent)]
#[derive(Clone)]
pub struct PyClient {
    pub client: Virgo,
}

#[pymethods]
impl PyClient {
    #[new]
    pub fn new(secret: &str, value: &str) -> PyResult<Self> {
        let rt = current();
        let _g = Handle::try_current().err().map(|_| rt.enter());

        let client = Virgo::new(secret, value)?;
        Ok(Self { client })
    }

    #[pyo3(signature = (table, timeout=None, opt=None))]
    pub fn scan(
        &self,
        table: &str,
        timeout: Option<Duration>,
        opt: Option<&Bound<'_, PyDict>>,
    ) -> PyResult<PyLazyFrame> {
        let with = || {
            current().block_on(async move {
                self.client.get_schema(table, timeout).await.map(Into::into)
            })
        };

        let schema = SCHEMA_CACHE.get_or_insert(table, with)?;

        let lf = self.scan_inner(table, opt, schema, timeout)?;

        Ok(lf.into())
    }
}

impl PyClient {
    fn scan_inner(
        &self,
        table: &str,
        opt: Option<&Bound<'_, PyDict>>,
        schema: SchemaRef,
        timeout: Option<Duration>,
    ) -> anyhow::Result<LazyFrame> {
        let opt = serde_json::to_value(&JsonDict { inner: opt })
            .map_err(|e| PyRuntimeError::new_err(e.to_string()))?;

        let mut opt = match opt {
            Value::Object(v) => v,
            Value::Null => serde_json::Map::new(),
            _ => bail!("opt must be object"),
        };

        let schema = if let Some(force_columns) = opt.get("force_columns") {
            let force_columns = force_columns
                .as_array()
                .unwrap()
                .iter()
                .map(|v| v.as_str().unwrap());

            Arc::new(schema.try_project(force_columns)?)
        } else {
            schema
        };

        let scan = if io_engine_enabled() {
            let scan = Injector::new(IoParam::Table {
                name: table.to_string(),
                opt,
                timeout,
                schema: schema.clone(),
            });

            Arc::new(scan) as Arc<dyn AnonymousScan>
        } else {
            if let Some(force_columns) = opt.remove("force_columns") {
                opt.insert("columns".to_string(), force_columns);
            }

            let scan = VirgoScan::new(self.client.clone(), table, opt, schema.clone(), timeout);

            Arc::new(scan) as Arc<dyn AnonymousScan>
        };

        let args = ScanArgsAnonymous {
            name: "TABLE",
            schema: Some(schema),
            ..Default::default()
        };

        LazyFrame::anonymous_scan(scan, args).map_err(Into::into)
    }
}
