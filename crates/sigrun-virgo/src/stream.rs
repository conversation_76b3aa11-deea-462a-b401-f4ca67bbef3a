use std::sync::Arc;

use chrono::{Duration, NaiveDateTime, NaiveTime};
use polars::{
    prelude::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Scan<PERSON>rgs<PERSON><PERSON><PERSON>ous, SchemaRef},
    time::ClosedWindow,
};
use polars_python::lazyframe::PyLazyFrame;
use pyo3::{
    exceptions::PyValueError, pybacked::PyBackedStr, pyclass, pyfunction, pymethods,
    types::PyAnyMethods, Bound, FromPyObject, PyAny, PyResult,
};
use pyo3_utils::StrOrT;
use sigrun_io_expr::streaming::{Injector, IoParam, StreamType, WindowsType};
use virgo::highfreq::stream::{bar_schema, order_level_schema};

use crate::highfreq::{highfreq_schema, PyDataType, PySecurityType};

#[pyfunction]
pub fn py_high_freq(secu_type: PySecurityType, data_type: PyDataType) -> WantWindows {
    WantWindows {
        stream_type: StreamType::HighFreq(secu_type.0, data_type.0),
    }
}

#[pyfunction]
pub fn order_level() -> WantWindows {
    WantWindows {
        stream_type: StreamType::OrderLevel,
    }
}

#[pyfunction]
pub fn high_freq_bar() -> WantWindows {
    WantWindows {
        stream_type: StreamType::Bar,
    }
}

#[pyclass]
pub struct WantWindows {
    stream_type: StreamType,
}

#[derive(Clone)]
#[pyclass]
pub struct PyWindowsType {
    inner: WindowsType,
}

#[pymethods]
impl PyWindowsType {
    #[staticmethod]
    pub fn tumbling(
        width: Duration,
        earliest: Option<StrOrT<NaiveTime>>,
        latest: Option<StrOrT<NaiveTime>>,
    ) -> Self {
        Self {
            inner: WindowsType::Tumbling {
                width,
                earliest: earliest.map(|t| t.inner).unwrap_or(NaiveTime::MIN),
                latest: latest.map(|t| t.inner).unwrap_or(NaiveDateTime::MAX.time()),
            },
        }
    }

    #[staticmethod]
    pub fn growing(
        width: Duration,
        earliest: Option<StrOrT<NaiveTime>>,
        latest: Option<StrOrT<NaiveTime>>,
    ) -> Self {
        Self {
            inner: WindowsType::Growing {
                width,
                earliest: earliest.map(|t| t.inner).unwrap_or(NaiveTime::MIN),
                latest: latest.map(|t| t.inner).unwrap_or(NaiveDateTime::MAX.time()),
            },
        }
    }
}

#[pymethods]
impl WantWindows {
    pub fn windows(&self, windows_type: PyWindowsType) -> anyhow::Result<PyLazyFrame> {
        let schema = stream_schema(&self.stream_type)?;
        let scan = Injector::new(IoParam::new(self.stream_type, windows_type.inner));

        let args = ScanArgsAnonymous {
            name: "STREAM",
            schema: Some(schema),
            ..Default::default()
        };

        Ok(LazyFrame::anonymous_scan(Arc::new(scan), args)?.into())
    }
}

fn stream_schema(stream_type: &StreamType) -> anyhow::Result<SchemaRef> {
    match stream_type {
        StreamType::OrderLevel => Ok(order_level_schema()),
        StreamType::Bar => Ok(bar_schema()),
        StreamType::HighFreq(secu_type, data_type) => highfreq_schema(*secu_type, *data_type),
    }
}

#[repr(transparent)]
pub struct Wrap<T>(pub T);

impl<'py> FromPyObject<'py> for Wrap<ClosedWindow> {
    fn extract_bound(ob: &Bound<'py, PyAny>) -> PyResult<Self> {
        let parsed = match &*ob.extract::<PyBackedStr>()? {
            "left" => ClosedWindow::Left,
            "right" => ClosedWindow::Right,
            "both" => ClosedWindow::Both,
            "none" => ClosedWindow::None,
            v => {
                return Err(PyValueError::new_err(format!(
                    "`closed` must be one of {{'left', 'right', 'both', 'none'}}, got {v}",
                )));
            }
        };
        Ok(Wrap(parsed))
    }
}
