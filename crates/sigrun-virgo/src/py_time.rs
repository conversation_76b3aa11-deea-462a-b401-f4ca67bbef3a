use chrono::{
    format::{parse, Parsed, StrftimeItems},
    NaiveDate, NaiveTime, ParseResult,
};
use pyo3::{
    exceptions::PyValueError,
    prelude::{Bound, PyAny},
    pybacked::PyBackedStr,
    FromPyObject, PyResult,
};

#[repr(transparent)]
pub struct PyTime(NaiveTime);

impl From<PyTime> for NaiveTime {
    fn from(value: PyTime) -> Self {
        value.0
    }
}

impl FromPyObject<'_> for PyTime {
    fn extract_bound(ob: &Bound<'_, PyAny>) -> PyResult<Self> {
        let t: TOrStr<NaiveTime> = TOrStr::extract_bound(ob)?;
        match t {
            TOrStr::T(t) => Ok(Self(t)),
            TOrStr::Str(s) => parse_time(&s).map(Self),
        }
    }
}

#[repr(transparent)]
pub struct PyDate(NaiveDate);

impl From<PyDate> for NaiveDate {
    fn from(value: PyDate) -> Self {
        value.0
    }
}

impl FromPyObject<'_> for PyDate {
    fn extract_bound(ob: &Bound<'_, PyAny>) -> PyResult<Self> {
        let t: TOrStr<NaiveDate> = TOrStr::extract_bound(ob)?;
        match t {
            TOrStr::T(t) => Ok(Self(t)),
            TOrStr::Str(s) => parse_date(&s).map(Self),
        }
    }
}

#[derive(FromPyObject)]
enum TOrStr<T> {
    T(T),
    Str(PyBackedStr),
}

fn parse_date(date: &str) -> PyResult<NaiveDate> {
    try_parse(
        date,
        [StrftimeItems::new("%Y-%m-%d"), StrftimeItems::new("%Y%m%d")],
        Parsed::to_naive_date,
    )
    .ok_or_else(|| {
        PyValueError::new_err(r##"date format error. should be one of %Y-%m-%d or %Y%m%d"##)
    })
}

fn parse_time(time: &str) -> PyResult<NaiveTime> {
    try_parse(
        time,
        [
            StrftimeItems::new("%H:%M:%S"),
            StrftimeItems::new("%H:%M:%S.%f"),
        ],
        Parsed::to_naive_time,
    )
    .ok_or_else(|| {
        PyValueError::new_err(r##"time format error. should be one of %H:%M:%S or %H:%M:%S.%f"##)
    })
}

fn try_parse<const N: usize, F, T>(input: &str, fmt: [StrftimeItems; N], f: F) -> Option<T>
where
    F: Fn(&Parsed) -> ParseResult<T>,
{
    fmt.into_iter().find_map(|fmt| {
        let mut parsed = Parsed::new();
        parse(&mut parsed, input, fmt).and_then(|_| f(&parsed)).ok()
    })
}
