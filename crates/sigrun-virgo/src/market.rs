use std::ops::Deref;

use chrono::NaiveDate;
use polars::prelude::Roll;
use polars_python::{expr::PyExpr, prelude::Wrap};
use pyo3::{pyclass, pymethods, PyResult};
use virgo::market::{trading_days::TradingDaysUpdater, MarketClient};

use super::py_time::PyDate;

#[pyclass]
#[repr(transparent)]
#[derive(Clone)]
pub struct PyTradingDays {
    inner: TradingDaysUpdater,
}

impl Deref for PyTradingDays {
    type Target = TradingDaysUpdater;

    fn deref(&self) -> &Self::Target {
        &self.inner
    }
}

#[pymethods]
impl PyTradingDays {
    #[new]
    pub fn new() -> PyResult<Self> {
        let inner = runtime::current().block_on(async move {
            let client = MarketClient::new_from_dyconfig().await;
            client.trading_day_updater().await
        })?;
        Ok(Self { inner })
    }

    pub fn trading_days(&self, from: PyDate, to: PyDate) -> Vec<NaiveDate> {
        self.inner
            .trading_days()
            .ranges(from.into()..=to.into())
            .into_iter()
            .collect()
    }

    pub fn trading_day(&self, date: PyDate, span: i32) -> Option<NaiveDate> {
        self.inner.trading_days().find(date.into(), span)
    }

    pub fn is_trading_day(&self, date: PyDate) -> bool {
        self.inner.trading_days().contains(&date.into())
    }

    pub fn add_trading_day(&self, input: PyExpr, n: PyExpr, roll: Wrap<Roll>) -> PyExpr {
        const WEEK_MASK: [bool; 7] = [true, true, true, true, true, false, false];

        input
            .inner
            .dt()
            .add_business_days(n.inner, WEEK_MASK, self.inner.holidays(), roll.0)
            .into()
    }
}

impl PyTradingDays {
    pub(crate) fn inner(&self) -> &TradingDaysUpdater {
        &self.inner
    }
}
