use std::{ops::Deref, str::FromStr, sync::LazyLock};

use chrono::{NaiveDate, NaiveTime};
use polars::prelude::*;
use polars_arrow::array::{BinaryViewArray, MutableBinaryViewArray};
use polars_python::{error::PyPolarsErr, lazyframe::PyLazyFrame};
use pyo3::{exceptions::PyValueError, prelude::*, pybacked::PyBackedStr, types::PyString};
use sigrun_io_expr::batch::{HighFreqParam, Injector, IoParam};
use sigrun_utils::env::io_engine_enabled;
use virgo::highfreq::{
    filter_symbols, filter_time, Freq, HighFreqReader, HighFreqType, SecurityType, SymbolArgs,
};

use crate::{market::PyTradingDays, table::PyClient};

use super::py_time::{PyDate, PyTime};

#[pyclass]
#[repr(transparent)]
pub struct PyHighFreqReader {
    reader: HighFreqReader,
}

impl From<HighFreqReader> for PyHighFreqReader {
    fn from(reader: HighFreqReader) -> Self {
        Self { reader }
    }
}

#[derive(Clone, Copy)]
#[pyclass]
pub struct PySecurityType(pub SecurityType);

#[pymethods]
impl PySecurityType {
    #[staticmethod]
    pub fn stock() -> Self {
        Self(SecurityType::Stock)
    }

    #[staticmethod]
    pub fn future() -> Self {
        Self(SecurityType::Future)
    }

    #[staticmethod]
    pub fn index() -> Self {
        Self(SecurityType::Index)
    }

    #[staticmethod]
    pub fn etf() -> Self {
        Self(SecurityType::Etf)
    }
}

#[derive(Clone, Copy)]
#[pyclass]
pub struct PyDataType(pub HighFreqType);

#[pymethods]
impl PyDataType {
    #[staticmethod]
    pub fn r#match() -> Self {
        Self(HighFreqType::Match)
    }

    #[staticmethod]
    pub fn order() -> Self {
        Self(HighFreqType::Order)
    }

    #[staticmethod]
    pub fn snapshot() -> Self {
        Self(HighFreqType::Snapshot)
    }

    #[staticmethod]
    pub fn bar(freq: &str) -> anyhow::Result<Self> {
        let freq = HighFreqType::Bar(Freq::from_str(freq)?);
        Ok(Self(freq))
    }
}

#[pymethods]
impl PyHighFreqReader {
    #[new]
    pub fn new(market_client: PyTradingDays) -> PyResult<Self> {
        let reader = HighFreqReader::new(market_client.inner().clone());
        Ok(Self { reader })
    }

    #[pyo3(signature = (secu_type, data_type, symbols, from_date, virgo, to_date, from_time, to_time, force_columns))]
    #[allow(clippy::too_many_arguments)]
    pub fn read_high_freq_helper(
        &self,
        secu_type: PySecurityType,
        data_type: PyDataType,
        symbols: &Bound<'_, PyAny>,
        from_date: PyDate,
        virgo: &PyClient,
        to_date: Option<PyDate>,
        from_time: Option<PyTime>,
        to_time: Option<PyTime>,
        force_columns: Option<Vec<PyBackedStr>>,
    ) -> PyResult<PyLazyFrame> {
        let secu_type = secu_type.0;
        let data_type = data_type.0;

        let symbols = parse_symbols(symbols)?;

        let from_date: NaiveDate = from_date.into();
        let to_date = to_date.map(|d| d.into()).unwrap_or(from_date);

        let from_time = from_time.map(Into::into);
        let to_time = to_time.map(Into::into);

        let ldf = self.read_history_high_freq(secu_type, data_type, from_date, to_date)?;

        let mut is_virgo_table = false;

        let ret = if let Some(ldf) = ldf {
            ldf
        } else {
            let today = chrono::Local::now().date_naive();

            if from_date == to_date && from_date == today {
                is_virgo_table = true;

                if data_type.is_bar() {
                    virgo
                        .scan(
                            &format!("market.{secu_type}.{data_type}.virgo_intra"),
                            None,
                            None,
                        )?
                        .ldf
                        .filter(col("date").eq(lit(today)))
                } else {
                    self.read_realtime_high_freq(secu_type, data_type, from_time, to_time)?
                }
            } else {
                return Err(anyhow::anyhow!(
                    "data not found, from_date: {from_date}, to_date: {to_date}",
                )
                .into());
            }
        };

        let lf = filter_symbols(ret, &symbols);

        let has_force_columns;
        let lf = if let Some(ref project) = force_columns {
            has_force_columns = true;
            lf.select(project.iter().map(|c| col(c.deref())).collect::<Vec<_>>())
        } else {
            has_force_columns = false;
            lf
        };

        let lf = if io_engine_enabled() && !is_virgo_table {
            let scan = Injector::new(IoParam::HighFreq {
                param: HighFreqParam {
                    secu_type,
                    data_type,
                    symbols,
                    from_date,
                    to_date,
                },
                subplan: Box::new(lf),
                has_force_columns,
            });

            let schema = highfreq_schema(secu_type, data_type)?;

            let schema = if let Some(force_columns) = force_columns {
                Arc::new(
                    schema
                        .try_project(force_columns.iter().map(|c| c.deref()))
                        .map_err(PyPolarsErr::from)?,
                )
            } else {
                schema
            };

            let args = ScanArgsAnonymous {
                name: "HIGHFREQ",
                schema: Some(schema),
                ..Default::default()
            };

            LazyFrame::anonymous_scan(Arc::new(scan), args).unwrap()
        } else {
            lf
        };

        Ok(filter_time(lf, from_time, to_time).into())
    }
}

impl PyHighFreqReader {
    pub fn read_history_high_freq(
        &self,
        secu_type: SecurityType,
        data_type: HighFreqType,
        from_date: NaiveDate,
        to_date: NaiveDate,
    ) -> anyhow::Result<Option<LazyFrame>> {
        self.reader
            .read_history_naive(secu_type, data_type, from_date, to_date)
    }

    pub fn read_realtime_high_freq(
        &self,
        secu_type: SecurityType,
        data_type: HighFreqType,
        from_time: Option<NaiveTime>,
        to_time: Option<NaiveTime>,
    ) -> anyhow::Result<LazyFrame> {
        let mut ldf = self
            .reader
            .read_realtime_naive(secu_type, data_type, from_time, to_time)?;

        if secu_type.is_stock() {
            let ascii_code_cols: &[Expr] = match data_type {
                HighFreqType::Match => {
                    const ASCII_CODE_COL: [Expr; 3] = [
                        Expr::Column(PlSmallStr::from_static("bs_flag")),
                        Expr::Column(PlSmallStr::from_static("trade_type")),
                        Expr::Column(PlSmallStr::from_static("function_code")),
                    ];
                    &ASCII_CODE_COL
                }
                HighFreqType::Order => {
                    const ASCII_CODE_COL: [Expr; 2] = [
                        Expr::Column(PlSmallStr::from_static("order_type")),
                        Expr::Column(PlSmallStr::from_static("function_code")),
                    ];
                    &ASCII_CODE_COL
                }
                _ => &[],
            };

            for name in ascii_code_cols {
                ldf = ldf.with_column(ascii_u8_to_str(name.clone()));
            }
        }

        Ok(ldf.with_columns([
            Expr::from(
                dtype_col(&DataType::Categorical(
                    Categories::global(),
                    Categories::global().mapping(),
                ))
                .as_selector(),
            )
            .cast(DataType::String),
            Expr::from(dtype_col(&DataType::Int32).as_selector()).cast(DataType::Int64),
            Expr::from(dtype_col(&DataType::Float32).as_selector()).cast(DataType::Float64),
        ]))
    }
}

fn ascii_u8_to_str(expr: Expr) -> Expr {
    expr.map(
        |col| {
            let col = col.u8()?;
            let chunks = col
                .data_views()
                .zip(col.iter_validities())
                .map(|(slice, validity)| {
                    let buf = MutableBinaryViewArray::with_capacity(slice.len());

                    let buf = slice.iter().fold(buf, |mut buf, &v| {
                        buf.push_value([v]);
                        buf
                    });

                    let arr: BinaryViewArray = buf.into();
                    let arr = unsafe { arr.to_utf8view_unchecked() };

                    arr.with_validity(validity.cloned())
                });

            let chunks = StringChunked::from_chunk_iter(col.name().clone(), chunks);
            Ok(Some(chunks.into_column()))
        },
        GetOutput::from_type(DataType::String),
    )
}

fn parse_symbols(symbols: &Bound<'_, PyAny>) -> PyResult<SymbolArgs> {
    if let Ok(s) = symbols.downcast::<PyString>() {
        return Ok(SymbolArgs::from(s.to_str()?));
    }

    if let Ok(s) = symbols.extract::<Vec<String>>() {
        return Ok(SymbolArgs::from(s));
    }

    Err(PyValueError::new_err("symbols must be a str or list[str]"))
}

pub fn highfreq_schema(
    secu_type: SecurityType,
    data_type: HighFreqType,
) -> anyhow::Result<SchemaRef> {
    match (secu_type, data_type) {
        (SecurityType::Stock, HighFreqType::Order) => Ok(stock_orders_schema()),
        (SecurityType::Stock, HighFreqType::Match) => Ok(stock_matches_schema()),
        (SecurityType::Stock, HighFreqType::Snapshot) => Ok(stock_snapshot_schema()),
        (SecurityType::Stock, HighFreqType::Bar(_)) => Ok(stock_bar_schema()),
        (SecurityType::Future, HighFreqType::Snapshot) => Ok(future_snapshot_schema()),
        (SecurityType::Index, HighFreqType::Snapshot) => Ok(index_snapshot_schema()),
        (SecurityType::Index, HighFreqType::Bar(_)) => Ok(index_bar_schema()),
        (SecurityType::Etf, HighFreqType::Bar(_)) => Ok(etf_bar_schema()),
        (secu_type, data_type) => anyhow::bail!(
            "unsupported highfreq schema: {:?}, {:?}",
            secu_type,
            data_type
        ),
    }
}

fn stock_matches_schema() -> SchemaRef {
    static SCHEMA: LazyLock<SchemaRef> = LazyLock::new(|| {
        Arc::new(Schema::from_iter([
            Field::new(PlSmallStr::from_static("symbol"), DataType::String),
            Field::new(
                PlSmallStr::from_static("date"),
                DataType::Datetime(TimeUnit::Nanoseconds, None),
            ),
            Field::new(
                PlSmallStr::from_static("time"),
                DataType::Duration(TimeUnit::Nanoseconds),
            ),
            Field::new(PlSmallStr::from_static("seq"), DataType::Int64),
            Field::new(PlSmallStr::from_static("price"), DataType::Float64),
            Field::new(PlSmallStr::from_static("volume"), DataType::Int64),
            Field::new(PlSmallStr::from_static("bs_flag"), DataType::String),
            Field::new(PlSmallStr::from_static("trade_type"), DataType::String),
            Field::new(PlSmallStr::from_static("function_code"), DataType::String),
            Field::new(PlSmallStr::from_static("ask_order_seq"), DataType::Int64),
            Field::new(PlSmallStr::from_static("bid_order_seq"), DataType::Int64),
            Field::new(PlSmallStr::from_static("exchange"), DataType::String),
        ]))
    });
    SCHEMA.clone()
}

fn stock_orders_schema() -> SchemaRef {
    static SCHEMA: LazyLock<SchemaRef> = LazyLock::new(|| {
        Arc::new(Schema::from_iter([
            Field::new(PlSmallStr::from_static("symbol"), DataType::String),
            Field::new(
                PlSmallStr::from_static("date"),
                DataType::Datetime(TimeUnit::Nanoseconds, None),
            ),
            Field::new(
                PlSmallStr::from_static("time"),
                DataType::Duration(TimeUnit::Nanoseconds),
            ),
            Field::new(PlSmallStr::from_static("seq"), DataType::Int64),
            Field::new(PlSmallStr::from_static("price"), DataType::Float64),
            Field::new(PlSmallStr::from_static("volume"), DataType::Int64),
            Field::new(PlSmallStr::from_static("order_type"), DataType::String),
            Field::new(PlSmallStr::from_static("function_code"), DataType::String),
            Field::new(PlSmallStr::from_static("exchange"), DataType::String),
        ]))
    });
    SCHEMA.clone()
}

fn stock_snapshot_schema() -> SchemaRef {
    static SCHEMA: LazyLock<SchemaRef> = LazyLock::new(|| {
        Arc::new(Schema::from_iter([
            Field::new(PlSmallStr::from_static("symbol"), DataType::String),
            Field::new(
                PlSmallStr::from_static("date"),
                DataType::Datetime(TimeUnit::Nanoseconds, None),
            ),
            Field::new(
                PlSmallStr::from_static("time"),
                DataType::Duration(TimeUnit::Nanoseconds),
            ),
            Field::new(PlSmallStr::from_static("last"), DataType::Float64),
            Field::new(PlSmallStr::from_static("pre_close"), DataType::Float64),
            Field::new(PlSmallStr::from_static("open"), DataType::Float64),
            Field::new(PlSmallStr::from_static("high"), DataType::Float64),
            Field::new(PlSmallStr::from_static("low"), DataType::Float64),
            Field::new(PlSmallStr::from_static("volume"), DataType::Int64),
            Field::new(PlSmallStr::from_static("turnover"), DataType::Float64),
            Field::new(PlSmallStr::from_static("acc_volume"), DataType::Int64),
            Field::new(PlSmallStr::from_static("acc_turnover"), DataType::Float64),
            Field::new(PlSmallStr::from_static("match_items"), DataType::Int64),
            Field::new(PlSmallStr::from_static("total_ask_qty"), DataType::Int64),
            Field::new(PlSmallStr::from_static("total_bid_qty"), DataType::Int64),
            Field::new(PlSmallStr::from_static("wavg_ask"), DataType::Float64),
            Field::new(PlSmallStr::from_static("wavg_bid"), DataType::Float64),
            Field::new(PlSmallStr::from_static("interest"), DataType::Float64),
            // Ask/Bid Levels 1-10
            Field::new(PlSmallStr::from_static("ask_price1"), DataType::Float64),
            Field::new(PlSmallStr::from_static("ask_volume1"), DataType::Int64),
            Field::new(PlSmallStr::from_static("bid_price1"), DataType::Float64),
            Field::new(PlSmallStr::from_static("bid_volume1"), DataType::Int64),
            Field::new(PlSmallStr::from_static("ask_price2"), DataType::Float64),
            Field::new(PlSmallStr::from_static("ask_volume2"), DataType::Int64),
            Field::new(PlSmallStr::from_static("bid_price2"), DataType::Float64),
            Field::new(PlSmallStr::from_static("bid_volume2"), DataType::Int64),
            Field::new(PlSmallStr::from_static("ask_price3"), DataType::Float64),
            Field::new(PlSmallStr::from_static("ask_volume3"), DataType::Int64),
            Field::new(PlSmallStr::from_static("bid_price3"), DataType::Float64),
            Field::new(PlSmallStr::from_static("bid_volume3"), DataType::Int64),
            Field::new(PlSmallStr::from_static("ask_price4"), DataType::Float64),
            Field::new(PlSmallStr::from_static("ask_volume4"), DataType::Int64),
            Field::new(PlSmallStr::from_static("bid_price4"), DataType::Float64),
            Field::new(PlSmallStr::from_static("bid_volume4"), DataType::Int64),
            Field::new(PlSmallStr::from_static("ask_price5"), DataType::Float64),
            Field::new(PlSmallStr::from_static("ask_volume5"), DataType::Int64),
            Field::new(PlSmallStr::from_static("bid_price5"), DataType::Float64),
            Field::new(PlSmallStr::from_static("bid_volume5"), DataType::Int64),
            Field::new(PlSmallStr::from_static("ask_price6"), DataType::Float64),
            Field::new(PlSmallStr::from_static("ask_volume6"), DataType::Int64),
            Field::new(PlSmallStr::from_static("bid_price6"), DataType::Float64),
            Field::new(PlSmallStr::from_static("bid_volume6"), DataType::Int64),
            Field::new(PlSmallStr::from_static("ask_price7"), DataType::Float64),
            Field::new(PlSmallStr::from_static("ask_volume7"), DataType::Int64),
            Field::new(PlSmallStr::from_static("bid_price7"), DataType::Float64),
            Field::new(PlSmallStr::from_static("bid_volume7"), DataType::Int64),
            Field::new(PlSmallStr::from_static("ask_price8"), DataType::Float64),
            Field::new(PlSmallStr::from_static("ask_volume8"), DataType::Int64),
            Field::new(PlSmallStr::from_static("bid_price8"), DataType::Float64),
            Field::new(PlSmallStr::from_static("bid_volume8"), DataType::Int64),
            Field::new(PlSmallStr::from_static("ask_price9"), DataType::Float64),
            Field::new(PlSmallStr::from_static("ask_volume9"), DataType::Int64),
            Field::new(PlSmallStr::from_static("bid_price9"), DataType::Float64),
            Field::new(PlSmallStr::from_static("bid_volume9"), DataType::Int64),
            Field::new(PlSmallStr::from_static("ask_price10"), DataType::Float64),
            Field::new(PlSmallStr::from_static("ask_volume10"), DataType::Int64),
            Field::new(PlSmallStr::from_static("bid_price10"), DataType::Float64),
            Field::new(PlSmallStr::from_static("bid_volume10"), DataType::Int64),
            Field::new(PlSmallStr::from_static("status"), DataType::String),
            Field::new(PlSmallStr::from_static("exchange"), DataType::String),
        ]))
    });
    SCHEMA.clone()
}

fn stock_bar_schema() -> SchemaRef {
    static SCHEMA: LazyLock<SchemaRef> = LazyLock::new(|| {
        Arc::new(Schema::from_iter([
            Field::new(PlSmallStr::from_static("symbol"), DataType::String),
            Field::new(
                PlSmallStr::from_static("date"),
                DataType::Datetime(TimeUnit::Nanoseconds, None),
            ),
            Field::new(
                PlSmallStr::from_static("time"),
                DataType::Duration(TimeUnit::Nanoseconds),
            ),
            Field::new(PlSmallStr::from_static("pre_close"), DataType::Float64),
            Field::new(PlSmallStr::from_static("open"), DataType::Float64),
            Field::new(PlSmallStr::from_static("high"), DataType::Float64),
            Field::new(PlSmallStr::from_static("low"), DataType::Float64),
            Field::new(PlSmallStr::from_static("close"), DataType::Float64),
            Field::new(PlSmallStr::from_static("volume"), DataType::Int64),
            Field::new(PlSmallStr::from_static("turnover"), DataType::Float64),
            Field::new(PlSmallStr::from_static("match_items"), DataType::Int64),
            Field::new(PlSmallStr::from_static("exchange"), DataType::String),
        ]))
    });
    SCHEMA.clone()
}

fn future_snapshot_schema() -> SchemaRef {
    static SCHEMA: LazyLock<SchemaRef> = LazyLock::new(|| {
        Arc::new(Schema::from_iter([
            Field::new(PlSmallStr::from_static("symbol"), DataType::String),
            Field::new(
                PlSmallStr::from_static("date"),
                DataType::Datetime(TimeUnit::Nanoseconds, None),
            ),
            Field::new(
                PlSmallStr::from_static("time"),
                DataType::Duration(TimeUnit::Nanoseconds),
            ),
            Field::new(PlSmallStr::from_static("pre_close"), DataType::Float64),
            Field::new(
                PlSmallStr::from_static("pre_open_interest"),
                DataType::Int64,
            ),
            Field::new(PlSmallStr::from_static("pre_settle"), DataType::Float64),
            Field::new(PlSmallStr::from_static("open"), DataType::Float64),
            Field::new(PlSmallStr::from_static("high"), DataType::Float64),
            Field::new(PlSmallStr::from_static("low"), DataType::Float64),
            Field::new(PlSmallStr::from_static("close"), DataType::Float64),
            Field::new(PlSmallStr::from_static("last"), DataType::Float64),
            Field::new(PlSmallStr::from_static("up_limit"), DataType::Float64),
            Field::new(PlSmallStr::from_static("down_limit"), DataType::Float64),
            Field::new(PlSmallStr::from_static("settle"), DataType::Float64),
            Field::new(PlSmallStr::from_static("acc_volume"), DataType::Int64),
            Field::new(PlSmallStr::from_static("acc_turnover"), DataType::Float64),
            Field::new(PlSmallStr::from_static("open_interest"), DataType::Int64),
            Field::new(PlSmallStr::from_static("pre_delta"), DataType::Float64),
            Field::new(PlSmallStr::from_static("cur_delta"), DataType::Float64),
            Field::new(PlSmallStr::from_static("ask_price1"), DataType::Float64),
            Field::new(PlSmallStr::from_static("ask_volume1"), DataType::Int64),
            Field::new(PlSmallStr::from_static("bid_price1"), DataType::Float64),
            Field::new(PlSmallStr::from_static("bid_volume1"), DataType::Int64),
            Field::new(PlSmallStr::from_static("exchange"), DataType::String),
        ]))
    });
    SCHEMA.clone()
}

fn etf_bar_schema() -> SchemaRef {
    static SCHEMA: LazyLock<SchemaRef> = LazyLock::new(|| {
        Arc::new(Schema::from_iter([
            Field::new(PlSmallStr::from_static("symbol"), DataType::String),
            Field::new(
                PlSmallStr::from_static("date"),
                DataType::Datetime(TimeUnit::Nanoseconds, None),
            ),
            Field::new(
                PlSmallStr::from_static("time"),
                DataType::Duration(TimeUnit::Nanoseconds),
            ),
            Field::new(PlSmallStr::from_static("pre_close"), DataType::Float64),
            Field::new(PlSmallStr::from_static("open"), DataType::Float64),
            Field::new(PlSmallStr::from_static("high"), DataType::Float64),
            Field::new(PlSmallStr::from_static("low"), DataType::Float64),
            Field::new(PlSmallStr::from_static("close"), DataType::Float64),
            Field::new(PlSmallStr::from_static("volume"), DataType::Int64),
            Field::new(PlSmallStr::from_static("turnover"), DataType::Float64),
            Field::new(PlSmallStr::from_static("open_interest"), DataType::Int64),
            Field::new(PlSmallStr::from_static("exchange"), DataType::String),
        ]))
    });
    SCHEMA.clone()
}

fn index_snapshot_schema() -> SchemaRef {
    static SCHEMA: LazyLock<SchemaRef> = LazyLock::new(|| {
        Arc::new(Schema::from_iter([
            Field::new(PlSmallStr::from_static("symbol"), DataType::String),
            Field::new(
                PlSmallStr::from_static("date"),
                DataType::Datetime(TimeUnit::Nanoseconds, None),
            ),
            Field::new(
                PlSmallStr::from_static("time"),
                DataType::Duration(TimeUnit::Nanoseconds),
            ),
            Field::new(PlSmallStr::from_static("last"), DataType::Float64),
            Field::new(PlSmallStr::from_static("pre_close"), DataType::Float64),
            Field::new(PlSmallStr::from_static("open"), DataType::Float64),
            Field::new(PlSmallStr::from_static("high"), DataType::Float64),
            Field::new(PlSmallStr::from_static("low"), DataType::Float64),
            Field::new(PlSmallStr::from_static("volume"), DataType::Int64),
            Field::new(PlSmallStr::from_static("turnover"), DataType::Float64),
            Field::new(PlSmallStr::from_static("exchange"), DataType::String),
        ]))
    });
    SCHEMA.clone()
}

fn index_bar_schema() -> SchemaRef {
    static SCHEMA: LazyLock<SchemaRef> = LazyLock::new(|| {
        Arc::new(Schema::from_iter([
            Field::new(PlSmallStr::from_static("symbol"), DataType::String),
            Field::new(
                PlSmallStr::from_static("date"),
                DataType::Datetime(TimeUnit::Nanoseconds, None),
            ),
            Field::new(
                PlSmallStr::from_static("time"),
                DataType::Duration(TimeUnit::Nanoseconds),
            ),
            Field::new(PlSmallStr::from_static("pre_close"), DataType::Float64),
            Field::new(PlSmallStr::from_static("open"), DataType::Float64),
            Field::new(PlSmallStr::from_static("high"), DataType::Float64),
            Field::new(PlSmallStr::from_static("low"), DataType::Float64),
            Field::new(PlSmallStr::from_static("close"), DataType::Float64),
            Field::new(PlSmallStr::from_static("volume"), DataType::Int64),
            Field::new(PlSmallStr::from_static("turnover"), DataType::Float64),
            Field::new(PlSmallStr::from_static("exchange"), DataType::String),
        ]))
    });
    SCHEMA.clone()
}

#[cfg(test)]
mod tests {
    use super::*;

    fn test_ascii_conversion<I, E, P, H>(
        input_data: I,
        expected_data: E,
        equals_fn: impl FnOnce(&DataFrame, &DataFrame) -> bool,
    ) where
        Series: NamedFrom<I, P> + NamedFrom<E, H>,
        P: ?Sized,
        H: ?Sized,
    {
        let df = df![
            "test" => input_data
        ]
        .unwrap();

        let expected = df![
            "test" => expected_data
        ]
        .unwrap();

        let converted = ascii_u8_to_str(col("test"));
        let result = df.lazy().with_column(converted).collect().unwrap();

        assert!(equals_fn(&expected, &result));
    }

    #[test]
    fn test_ascii_u8_to_str() {
        let ascii_code = (0..128).collect::<Vec<u8>>();
        let ascii_chr = ascii_code
            .iter()
            .map(|&i| (i as char).to_string())
            .collect::<Vec<String>>();

        test_ascii_conversion(ascii_code, ascii_chr, DataFrame::equals);
    }

    #[test]
    fn test_ascii_u8_to_str_with_nulls() {
        let ascii_code = {
            let this = (0..128u8).map(Some);
            itertools::intersperse(this, None)
        }
        .collect::<Vec<Option<u8>>>();

        let ascii_chr = ascii_code
            .iter()
            .map(|i| i.map(|i| (i as char).to_string()))
            .collect::<Vec<Option<String>>>();

        test_ascii_conversion(ascii_code, ascii_chr, DataFrame::equals_missing);
    }
}
