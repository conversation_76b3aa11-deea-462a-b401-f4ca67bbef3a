use std::future::Future;

use polars::prelude::SchemaRef;
use quick_cache::{sync::C<PERSON>, <PERSON>Weighter};

pub struct SchemaCache {
    inner: Cache<String, SchemaRef, UnitWeighter, ahash::RandomState>,
}

impl SchemaCache {
    pub fn new(cap: usize) -> Self {
        Self {
            inner: Cache::with(
                cap,
                cap as u64,
                Default::default(),
                Default::default(),
                Default::default(),
            ),
        }
    }

    pub fn get_or_insert<E>(
        &self,
        key: &str,
        with: impl FnOnce() -> Result<SchemaRef, E>,
    ) -> Result<SchemaRef, E> {
        self.inner.get_or_insert_with(key, with)
    }

    pub async fn get_or_insert_async<E>(
        &self,
        key: &str,
        with: impl Future<Output = Result<SchemaRef, E>>,
    ) -> Result<SchemaRef, E> {
        self.inner.get_or_insert_async(key, with).await
    }
}
