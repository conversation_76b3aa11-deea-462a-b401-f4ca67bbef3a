use std::sync::OnceLock;

use tokio::runtime::{Builder, Runtime};

static RUNTIME: OnceLock<Runtime> = OnceLock::new();

pub fn current() -> &'static Runtime {
    RUNTIME.get_or_init(|| {
        Builder::new_multi_thread()
            .max_blocking_threads(24)
            .enable_all()
            .build()
            .unwrap()
    })
}

// thread_local! {
//     pub static RUNTIME: Runtime = tokio::runtime::Builder::new_current_thread()
//         .enable_io()
//         .build()
//         .unwrap();
// }
