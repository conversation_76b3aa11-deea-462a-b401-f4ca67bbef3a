use std::collections::VecDeque;

use ahash::{HashMap, HashMapExt};
use indexmap::IndexMap;
use polars::{
    error::{polars_bail, polars_err, PolarsResult},
    lazy::{dsl::Expr, frame::<PERSON><PERSON><PERSON><PERSON><PERSON>},
};

// TODO: try expr arena
pub fn with_columns_expr(mut ldf: LazyFrame, exprs: Vec<Expr>) -> PolarsResult<LazyFrame> {
    split_stage(
        exprs.iter(),
        ldf.collect_schema()?.iter_names(),
        |stage, ldf| ldf.with_columns(stage),
        ldf,
    )
}

fn split_stage<'a, I, F, ACC, R, S>(
    input: I,
    root: R,
    mut fold: F,
    mut init: ACC,
) -> PolarsResult<ACC>
where
    I: Iterator<Item = &'a Expr>,
    F: FnMut(Vec<Expr>, ACC) -> ACC,
    R: IntoIterator<Item = S>,
    S: AsRef<str>,
{
    let mut in_degree = IndexMap::with_hasher(ahash::RandomState::new());
    let mut adj = HashMap::new();
    let mut expr_lookup = HashMap::new();

    let mut queue = VecDeque::new();

    // build graph
    for expr in input {
        let (name, deps) = analyse_expr(expr)?;
        if expr_lookup.insert(name, expr).is_some() {
            polars_bail!(ComputeError: "duplicate column name: {}, expr: {}", name, expr);
        }

        if deps.is_empty() {
            queue.push_front(name);
        } else {
            deps.iter().for_each(|dep| {
                adj.entry(*dep)
                    .and_modify(|adj_nodes: &mut Vec<_>| adj_nodes.push(name))
                    .or_insert_with(|| vec![name]);
            });

            in_degree.insert(name, deps.len());
        }
    }

    // initial setup queue
    for name in root {
        let Some(nodes) = adj.get(name.as_ref()) else {
            continue;
        };

        for node in nodes {
            let Some(cnt) = in_degree.get_mut(node) else {
                continue;
            };

            if *cnt == 1 {
                queue.push_front(node);
            }

            *cnt = cnt.saturating_sub(1);
        }
    }

    // bfs
    loop {
        let len = queue.len();
        if len == 0 {
            break;
        }

        let mut level = Vec::with_capacity(len);

        for _ in 0..len {
            let name = queue.pop_back().unwrap();
            let expr = *expr_lookup
                .get(name)
                .ok_or_else(|| polars_err!(ComputeError: "expr not found"))?;

            level.push(expr.clone());

            let Some(nodes) = adj.get(name) else {
                continue;
            };

            for node in nodes {
                let Some(cnt) = in_degree.get_mut(node) else {
                    continue;
                };

                // 不能 cnt == 0, 否则会重复
                if *cnt == 1 {
                    queue.push_front(node);
                }

                *cnt = cnt.saturating_sub(1);
            }
        }

        init = fold(level, init);
    }

    let init = in_degree
        .iter()
        .filter(|(_, &cnt)| cnt > 0)
        .map(|(name, _)| expr_lookup.get(name).unwrap())
        .fold(init, |acc, &expr| fold(vec![expr.clone()], acc));

    Ok(init)
}

fn analyse_expr(expr: &Expr) -> PolarsResult<(&str, Vec<&str>)> {
    use Expr::*;
    let (name, deps) =
        expr.into_iter()
            .try_fold((None, vec![]), |(opt, mut deps), expr| match expr {
                Alias(_, name) => {
                    let name = opt.unwrap_or(&**name);
                    Ok((Some(name), deps))
                }
                Column(dep) => {
                    deps.push(&**dep);
                    Ok((opt, deps))
                }

                Selector(_) => {
                    polars_bail!(ComputeError: "selector not supported at this point")
                }
                KeepName(_) | RenameAlias { .. } => {
                    polars_bail!(ComputeError: "expr name extension not supported at this point")
                }
                SubPlan(_, _) => {
                    polars_bail!(ComputeError: "subplan not supported at this point")
                }
                _ => Ok((opt, deps)),
            })?;

    if let Some(name) = name {
        Ok((name, deps))
    } else {
        polars_bail!(ComputeError: "expr name not found")
    }
}

#[cfg(test)]
mod tests {
    use polars::{
        df,
        lazy::{dsl::col, frame::IntoLazy},
        prelude::*,
    };

    use super::*;

    fn check(expr: Vec<Expr>, expected: Vec<Vec<Expr>>, root: &[&str]) {
        let ret = split_stage(
            expr.iter(),
            root.iter(),
            |stage, mut ret| {
                ret.push(stage);
                ret
            },
            vec![],
        );

        assert_eq!(ret.unwrap(), expected)
    }

    #[test]
    fn test_multi_alias_analyse() {
        let delta = duration(DurationArgs::default().with_milliseconds(lit(500)));

        let expr = when(col("closest").gt(delta))
            .then(lit(1))
            .otherwise(lit(0))
            .alias("test");

        let (name, _) = analyse_expr(&expr).unwrap();
        assert_eq!(name, "test");
    }

    #[test]
    fn test_expr_wihout_deps() {
        let expr = [
            lit(1).alias("const"),
            (col("a") + col("b")).alias("sum0"),
            (col("c") + col("d")).alias("sum1"),
        ];

        check(
            expr.to_vec(),
            vec![expr.to_vec()],
            &["a", "b", "c", "d", "e"],
        );
    }

    #[test]
    fn test_expr_override() {
        let expr = [(col("a") + lit(1)).alias("a")];

        check(
            expr.to_vec(),
            expr.iter().map(|e| vec![e.clone()]).collect(),
            &["a"],
        );
    }

    #[test]
    fn test_insert_order() {
        let expr = [
            col("a").alias("a"),
            col("b").alias("b"),
            col("c").alias("c"),
            col("d").alias("d"),
            col("e").alias("e"),
            col("f").alias("f"),
            col("g").alias("g"),
            col("h").alias("h"),
        ];

        check(
            expr.to_vec(),
            expr.iter().map(|e| vec![e.clone()]).collect(),
            &[],
        );

        let expr = [
            (col("a") + col("b")).alias("sum0"),
            (col("c") + col("d")).alias("sum1"),
            (col("sum0") + col("sum1")).alias("sum2"),
            // col("d").alias("d"),
            col("e").alias("e"),
            col("f").alias("f"),
            col("g").alias("g"),
            col("h").alias("h"),
        ];

        let expected = vec![
            vec![
                (col("a") + col("b")).alias("sum0"),
                (col("c") + col("d")).alias("sum1"),
            ],
            vec![(col("sum0") + col("sum1")).alias("sum2")],
            vec![col("e").alias("e")],
            vec![col("f").alias("f")],
            vec![col("g").alias("g")],
            vec![col("h").alias("h")],
        ];

        check(expr.to_vec(), expected, &["a", "b", "c", "d"]);
    }

    #[test]
    fn split_expr_test() {
        let expr = [
            (col("a") + col("b")).alias("sum0"),
            (col("c") + col("d")).alias("sum1"),
        ];

        check(
            expr.to_vec(),
            vec![expr.to_vec()],
            &["a", "b", "c", "d", "e"],
        );
    }

    #[test]
    fn split_staged_expr_test() {
        let expr = [
            (col("a") + col("b")).alias("sum0"),
            (col("c") + col("d")).alias("sum1"),
            (col("sum0") + col("sum1")).alias("sum2"),
        ];

        let expected = vec![
            vec![
                (col("a") + col("b")).alias("sum0"),
                (col("c") + col("d")).alias("sum1"),
            ],
            vec![(col("sum0") + col("sum1")).alias("sum2")],
        ];

        check(expr.to_vec(), expected, &["a", "b", "c", "d"]);

        let expr = [
            (col("a") + col("b")).alias("sum0"),
            (col("c") + col("d")).alias("sum1"),
            (col("sum0") + col("sum1")).alias("sum2"),
            (col("sum0") + col("sum1")).alias("sum3"),
            (col("sum2") + col("sum3")).alias("sum4"),
        ];

        let expected = vec![
            vec![
                (col("a") + col("b")).alias("sum0"),
                (col("c") + col("d")).alias("sum1"),
            ],
            vec![
                (col("sum0") + col("sum1")).alias("sum2"),
                (col("sum0") + col("sum1")).alias("sum3"),
            ],
            vec![(col("sum2") + col("sum3")).alias("sum4")],
        ];

        check(expr.to_vec(), expected, &["a", "b", "c", "d"]);

        let expr = [
            col("a").alias("sum0"),
            col("a").alias("sum1"),
            (col("sum0") + col("sum1")).alias("sum2"),
        ];

        let expected = vec![
            vec![col("a").alias("sum0"), col("a").alias("sum1")],
            vec![(col("sum0") + col("sum1")).alias("sum2")],
        ];

        check(expr.to_vec(), expected, &["a", "b", "c", "d"]);
    }

    #[test]
    fn column_not_found_test() {
        let lf = df!("a" => [1, 2, 3], "b" => [4, 5, 6], "c" => [7, 8, 9], "d" => [10, 11, 12])
            .unwrap()
            .lazy();

        let expr = [
            (col("a") + col("b")).alias("sum0"),
            (col("c") + col("d")).alias("sum1"),
            (col("sum0") + col("sum3")).alias("sum2"),
        ];

        let ret = split_stage(
            expr.iter(),
            ["a", "b", "c", "d"].iter(),
            |stage, lf| lf.with_columns(stage),
            lf.clone(),
        )
        .unwrap();

        assert!(matches!(
            ret.collect().map_err(|e| { e.context_trace() }),
            Err(polars::error::PolarsError::ColumnNotFound { .. })
        ));

        let expr = [(col("a") + col("e")).alias("sum0")];

        let ret = split_stage(
            expr.iter(),
            ["a", "b", "c", "d"].iter(),
            |stage, lf| lf.with_columns(stage),
            lf.clone(),
        )
        .unwrap();

        assert!(matches!(
            ret.collect().map_err(|e| e.context_trace()),
            Err(polars::error::PolarsError::ColumnNotFound { .. })
        ));

        let expr = [
            (col("a") + col("b")).alias("sum0"),
            (col("c") + col("d")).alias("sum1"),
            (col("a") + col("sum3")).alias("sum2"),
        ];

        let ret = split_stage(
            expr.iter(),
            ["a", "b", "c", "d"].iter(),
            |stage, lf| lf.with_columns(stage),
            lf.clone(),
        )
        .unwrap();

        assert!(matches!(
            ret.collect().map_err(|e| e.context_trace()),
            Err(polars::error::PolarsError::ColumnNotFound { .. })
        ));
    }

    #[test]
    fn duplicate_column_name_test() {
        let lf = df!("a" => [1, 2, 3], "b" => [4, 5, 6], "c" => [7, 8, 9], "d" => [10, 11, 12])
            .unwrap()
            .lazy();

        let expr = [
            (col("a") + col("b")).alias("sum0"),
            (col("a") + col("e")).alias("sum0"),
            (col("c") + col("d")).alias("sum1"),
            (col("a") + col("sum1")).alias("sum2"),
        ];

        let ret = split_stage(
            expr.iter(),
            ["a"].iter(),
            |stage, lf| lf.with_columns(stage),
            lf.clone(),
        );

        assert!(matches!(
            ret.map_err(|e| e.context_trace()),
            Err(polars::error::PolarsError::ComputeError { .. })
        ));
    }
}
