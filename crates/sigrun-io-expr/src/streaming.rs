use std::sync::{Arc, OnceLock};

use chrono::NaiveTime;
use polars::{
    error::{ErrString, PolarsError, PolarsResult},
    frame::DataFrame,
    prelude::{AnonymousScan, AnonymousScanArgs},
};
use virgo::highfreq::{HighFreqType, SecurityType};

#[derive(Debu<PERSON>, <PERSON>lone, Hash, Eq, PartialEq)]
pub struct IoParam {
    pub stream_type: StreamType,
    pub windows_type: WindowsType,
}

impl IoParam {
    pub fn new(stream_type: StreamType, windows_type: WindowsType) -> Self {
        Self {
            stream_type,
            windows_type,
        }
    }
}

#[derive(Debug, <PERSON>lone, Hash, Eq, PartialEq, Copy)]
pub enum StreamType {
    OrderLevel,
    Bar,
    HighFreq(SecurityType, HighFreqType),
}

#[derive(Debug, <PERSON>lone, Copy, <PERSON>h, Eq, PartialEq)]
pub enum MockStream {
    Order,
    Transaction,
}

#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, Hash, Eq, PartialEq)]
pub enum WindowsType {
    Dummy,
    Growing {
        width: chrono::Duration,
        earliest: NaiveTime,
        latest: NaiveTime,
    },
    Tumbling {
        width: chrono::Duration,
        earliest: NaiveTime,
        latest: NaiveTime,
    },
}

#[derive(Clone)]
pub struct Injector {
    df: OnceLock<Result<DataFrame, Arc<anyhow::Error>>>,
    param: IoParam,
}

impl Injector {
    #[inline]
    pub fn params(&self) -> &IoParam {
        &self.param
    }

    #[inline]
    pub fn new(param: IoParam) -> Self {
        Self {
            df: OnceLock::new(),
            param,
        }
    }

    #[inline]
    pub fn inject(&self, df: Result<DataFrame, Arc<anyhow::Error>>) {
        let _t = self.df.set(df);
        debug_assert!(_t.is_ok());
    }
}

impl AnonymousScan for Injector {
    #[inline]
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }

    fn scan(&self, _: AnonymousScanArgs) -> PolarsResult<DataFrame> {
        self.df
            .get()
            .ok_or_else(|| {
                PolarsError::ComputeError(ErrString::new_static(
                    "df not set. May be you call collect in sigrun.run?",
                ))
            })?
            .as_ref()
            .cloned()
            .map_err(|e| PolarsError::ComputeError(ErrString::from(e.to_string())))
    }

    #[inline]
    fn allows_projection_pushdown(&self) -> bool {
        true
    }

    #[inline]
    fn allows_predicate_pushdown(&self) -> bool {
        true
    }

    // #[inline]
    // fn eq(&self, other: &dyn AnonymousScan) -> bool {
    //     if let Some(other) = other.as_any().downcast_ref::<Injector>() {
    //         self.param == other.param
    //     } else {
    //         false
    //     }
    // }
}
