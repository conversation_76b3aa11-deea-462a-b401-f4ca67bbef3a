use std::{
    fmt::{Debug, Display, Formatter},
    hash::Hash,
    sync::{LazyLock, OnceLock},
};

use chrono::NaiveDate;
use polars::{error::ErrString, prelude::*};
use serde_json::Value;
use virgo::highfreq::{HighFreqType, SecurityType, SymbolArgs};

#[derive(Clone)]
pub enum IoParam {
    Table {
        name: String,
        opt: serde_json::Map<String, Value>,
        timeout: Option<std::time::Duration>,
        schema: SchemaRef,
    },
    HighFreq {
        has_force_columns: bool,
        param: HighFreqParam,
        subplan: Box<LazyFrame>,
    },
}

impl IoParam {
    #[inline]
    pub fn table_info(&self) -> Option<(String, &serde_json::Map<String, Value>)> {
        match self {
            IoParam::Table { name, opt, .. } => Some((name.clone(), opt)),
            _ => None,
        }
    }
}

impl Display for IoParam {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        match self {
            IoParam::Table { name, .. } => write!(f, "virgo.table.scan(\"{name}\")"),
            IoParam::HighFreq { param, .. } => {
                write!(f, "virgo.{}.{}", param.secu_type, param.data_type)
            }
        }
    }
}

impl Debug for IoParam {
    fn fmt(&self, f: &mut Formatter) -> std::fmt::Result {
        match self {
            IoParam::Table { name, opt, .. } => f
                .debug_struct("Table")
                .field("name", &name)
                .field("opt", &opt)
                .finish(),
            IoParam::HighFreq {
                param,
                subplan: _,
                has_force_columns,
            } => f
                .debug_struct("HighFreq")
                .field("param", &param)
                .field("has_force_columns", &has_force_columns)
                .finish(),
        }
    }
}

impl Eq for IoParam {}

impl PartialEq for IoParam {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (
                Self::Table {
                    name: l_name,
                    opt: l_opt,
                    timeout: _,
                    schema: _,
                },
                Self::Table {
                    name: r_name,
                    opt: r_opt,
                    timeout: _,
                    schema: _,
                },
            ) => l_name == r_name && l_opt == r_opt,
            (
                Self::HighFreq {
                    param: l0,
                    subplan: _,
                    has_force_columns: _,
                },
                Self::HighFreq {
                    param: r0,
                    subplan: _,
                    has_force_columns: _,
                },
            ) => l0 == r0,
            _ => false,
        }
    }
}

#[derive(Clone, Debug, Hash, Eq, PartialEq)]
pub struct HighFreqParam {
    pub secu_type: SecurityType,
    pub data_type: HighFreqType,
    pub from_date: NaiveDate,
    pub to_date: NaiveDate,
    pub symbols: SymbolArgs,
}

impl Display for HighFreqParam {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "{}.{}.{}.{}.{}",
            self.secu_type, self.data_type, self.from_date, self.to_date, self.symbols
        )
    }
}

impl Hash for IoParam {
    fn hash<H>(&self, state: &mut H)
    where
        H: std::hash::Hasher,
    {
        match self {
            IoParam::Table { name, opt, .. } => {
                name.hash(state);
                opt.hash(state);
            }
            IoParam::HighFreq { param, .. } => {
                param.hash(state);
            }
        }
    }
}

#[derive(Clone)]
pub struct Injector {
    df: OnceLock<Result<DataFrame, Arc<anyhow::Error>>>,
    param: IoParam,
}

impl Injector {
    #[inline]
    pub fn has_force_columns(&self) -> bool {
        match &self.param {
            IoParam::Table { opt, .. } => opt.contains_key("force_columns"),
            IoParam::HighFreq {
                has_force_columns, ..
            } => *has_force_columns,
        }
    }

    #[inline]
    pub fn params(&self) -> &IoParam {
        &self.param
    }

    #[inline]
    pub fn new(param: IoParam) -> Self {
        Self {
            df: OnceLock::new(),
            param,
        }
    }

    #[inline]
    pub fn inject(&self, df: Result<DataFrame, Arc<anyhow::Error>>) {
        let _ = self.df.set(df);
    }
}

impl AnonymousScan for Injector {
    #[inline]
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }

    fn scan(&self, _: AnonymousScanArgs) -> PolarsResult<DataFrame> {
        self.df
            .get()
            .ok_or_else(|| {
                PolarsError::ComputeError(ErrString::new_static(
                    "df not set. May be you call collect in sigrun.run?",
                ))
            })?
            .as_ref()
            .cloned()
            .map_err(|e| PolarsError::ComputeError(ErrString::from(e.to_string())))
    }

    #[inline]
    fn allows_projection_pushdown(&self) -> bool {
        !self.has_force_columns()
    }

    #[inline]
    fn allows_predicate_pushdown(&self) -> bool {
        static ALLOWED_PUSHDOWN: LazyLock<bool> = LazyLock::new(|| {
            std::env::var("DISABLE_HIGHFREQ_PREDICATE_PUSHDOWN").map_or(true, |v| v != "true")
        });

        match &self.param {
            IoParam::Table { .. } => false,
            IoParam::HighFreq { .. } => *ALLOWED_PUSHDOWN,
        }
    }

    // #[inline]
    // fn eq(&self, other: &dyn AnonymousScan) -> bool {
    //     if let Some(other) = other.as_any().downcast_ref::<Injector>() {
    //         self.param == other.param
    //     } else {
    //         false
    //     }
    // }
}
