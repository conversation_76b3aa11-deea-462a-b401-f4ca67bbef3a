use std::{
    fmt::Debug,
    ops::Deref,
    sync::{
        Arc,
        atomic::{AtomicI64, Ordering},
    },
};

use super::time_utils::NanoSecs;

pub struct Builder {
    idx: usize,
    len: usize,
    holder: Arc<WatermarkHolder>,
}

impl Builder {
    pub fn create_updater(&mut self) -> Updater {
        let idx = self.idx;
        self.idx += 1;

        assert!(idx < self.len);

        Updater {
            idx: Index(idx),
            holder: self.holder.clone(),
        }
    }
}

pub struct Updater {
    idx: Index,
    holder: Arc<WatermarkHolder>,
}

impl Debug for Updater {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("Updater").field("idx", &self.idx).finish()
    }
}

impl Deref for Updater {
    type Target = WatermarkHolder;

    fn deref(&self) -> &Self::Target {
        &self.holder
    }
}

impl Updater {
    pub fn update(&self, watermark: NanoSecs) {
        self.holder.update(watermark, self.idx);
    }
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, PartialEq, Eq, Hash)]
struct Index(usize);

pub struct WatermarkHolder {
    watermarks: Vec<AtomicI64>,
    last_present_watermark: AtomicI64,
}

impl WatermarkHolder {
    pub fn builder(len: usize) -> Builder {
        Builder {
            idx: 0,
            holder: Arc::new(WatermarkHolder {
                watermarks: std::iter::repeat_with(|| AtomicI64::new(0))
                    .take(len)
                    .collect(),
                last_present_watermark: AtomicI64::new(0),
            }),
            len,
        }
    }

    pub fn last_present_watermark(&self) -> NanoSecs {
        NanoSecs::from_nanos(self.last_present_watermark.load(Ordering::Acquire))
    }

    fn update(&self, watermark: NanoSecs, index: Index) {
        // Safety: dispatch slot in builder
        let slot = unsafe { self.watermarks.get_unchecked(index.0) };

        slot.fetch_max(watermark.into_nanos(), Ordering::Release);

        let op = self
            .watermarks
            .iter()
            .map(|w| w.load(Ordering::Acquire))
            .min();

        // Safety: check none empty in builder
        let new_watermark = unsafe { op.unwrap_unchecked() };

        self.last_present_watermark
            .fetch_max(new_watermark, Ordering::Release);
    }
}
