mod aggregator;
mod history_wrapper;
mod stream_manager;

pub use aggregator::AggBatch;
use polars::frame::DataFrame;
pub use stream_manager::StreamManager;

#[derive(Default, Debug, Clone)]
pub struct WithOffset<T> {
    pub offset: usize,
    pub item: T,
}

impl<T> WithOffset<T> {
    pub fn new(offset: usize, item: T) -> Self {
        Self { offset, item }
    }
}

impl WithOffset<DataFrame> {
    pub fn append(&mut self, other: Self) {
        self.item.vstack_mut_owned_unchecked(other.item);
        self.offset = self.offset.max(other.offset);
    }
}
