use std::{
    mem::{replace, take},
    pin::Pin,
    task::{Context, Poll, ready},
    time::Duration,
};

use bon::Builder;
use futures_core::Stream;
use futures_util::FutureExt;
use pin_project::pin_project;
use polars::frame::DataFrame;
use polars_convert::ToDataFrame;
use tokio::time::{Instant, Sleep};

use crate::streaming::time_utils::NanoSecs;

use super::WithOffset;

#[derive(Clone)]
pub struct AggBatch {
    pub batch: WithOffset<DataFrame>,
    pub event_time: NanoSecs,
}

#[pin_project]
#[derive(Builder)]
pub struct RecordAggregator<St, T> {
    #[builder(name = "batch_size", with = |s: usize| Pending::new(s))]
    pending: Pending<T>,
    #[builder(skip)]
    end: bool,
    #[pin]
    stream: St,
    #[builder(with = |d: Duration| Linger::from(d))]
    linger: Option<Linger>,
}

struct Pending<T> {
    items: Vec<T>,
    max_offset: usize,
    batch_size: usize,
    event_time: NanoSecs,
}

impl<T> Pending<T> {
    fn new(batch_size: usize) -> Self {
        Self {
            items: Vec::with_capacity(batch_size),
            max_offset: 0,
            batch_size,
            event_time: NanoSecs::default(),
        }
    }
}

impl<T: ToDataFrame> Pending<T> {
    fn push(&mut self, with_offset: WithOffset<T>) {
        self.event_time = self
            .event_time
            .max(NanoSecs::from_nanos(with_offset.item.timestamp_ns()));

        self.max_offset = self.max_offset.max(with_offset.offset);

        self.items.push(with_offset.item);
    }

    fn try_flush(&mut self) -> Option<AggBatch> {
        if self.items.len() >= self.batch_size {
            Some(self.flush_inner())
        } else {
            None
        }
    }

    fn force_flush(&mut self) -> Option<AggBatch> {
        if !self.items.is_empty() {
            Some(self.flush_inner())
        } else {
            None
        }
    }

    fn flush_inner(&mut self) -> AggBatch {
        debug_assert!(!self.items.is_empty());
        debug_assert!(self.max_offset > 0);

        let df = T::to_dataframe(replace(
            &mut self.items,
            Vec::with_capacity(self.batch_size),
        ));
        AggBatch {
            batch: WithOffset {
                item: df,
                offset: take(&mut self.max_offset),
            },
            event_time: take(&mut self.event_time),
        }
    }
}

struct Linger {
    duration: Duration,
    sleep: Pin<Box<Sleep>>,
}

impl From<Duration> for Linger {
    fn from(value: Duration) -> Self {
        Self {
            duration: value,
            sleep: Box::pin(tokio::time::sleep(value)),
        }
    }
}

impl Linger {
    fn reset(&mut self) {
        self.sleep.as_mut().reset(Instant::now() + self.duration);
    }
}

impl<St, T, E> Stream for RecordAggregator<St, T>
where
    St: Stream<Item = Result<WithOffset<T>, E>>,
    T: ToDataFrame,
{
    type Item = Result<AggBatch, E>;

    fn poll_next(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        let mut this = self.project();

        if *this.end {
            return Poll::Ready(None);
        }

        loop {
            if let Some(batch) = this.pending.try_flush() {
                if let Some(l) = this.linger.as_mut() {
                    l.reset()
                }

                return Poll::Ready(Some(Ok(batch)));
            }

            if let Some(linger) = this.linger.as_mut() {
                if let Poll::Ready(()) = linger.sleep.as_mut().poll_unpin(cx) {
                    linger.reset();

                    if let Some(batch) = this.pending.force_flush() {
                        return Poll::Ready(Some(Ok(batch)));
                    }
                }
            }

            match ready!(this.stream.as_mut().poll_next(cx)) {
                Some(Ok(record)) => this.pending.push(record),
                Some(Err(e)) => return Poll::Ready(Some(Err(e))),
                None => {
                    return if let Some(batch) = this.pending.force_flush() {
                        *this.end = true;

                        Poll::Ready(Some(Ok(batch)))
                    } else {
                        Poll::Ready(None)
                    };
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use std::convert::Infallible;

    use super::*;
    use chrono::Local;
    use futures_util::{StreamExt, stream};
    use polars_convert::derive::ToDataFrame;

    #[derive(Debug, Clone, ToDataFrame)]
    struct TestRecord {
        value: i32,
        timestamp: i64,
    }

    fn make_streams(
        cnt: i32,
        throttle: Duration,
    ) -> impl Stream<Item = Result<WithOffset<TestRecord>, Infallible>> {
        let inner = stream::iter((0..cnt).map(|i| {
            Ok(WithOffset {
                item: TestRecord {
                    value: i,
                    timestamp: Local::now().to_utc().timestamp_nanos_opt().unwrap().into(),
                },
                offset: i as usize + 1,
            })
        }));

        if throttle > Duration::ZERO {
            Box::pin(tokio_stream::StreamExt::throttle(inner, throttle))
                as Pin<Box<dyn Stream<Item = _>>>
        } else {
            Box::pin(inner) as Pin<Box<dyn Stream<Item = _>>>
        }
    }

    #[tokio::test]
    async fn test_exact_agg() {
        let stream = make_streams(4, Duration::ZERO);

        let mut aggregator = RecordAggregator::builder()
            .stream(stream)
            .batch_size(2)
            .build();

        let df = aggregator.next().await.unwrap().unwrap().batch.item;
        assert_eq!(df.height(), 2);
        assert_eq!(df.column("value").unwrap().i32().unwrap().get(0), Some(0));
        assert_eq!(df.column("value").unwrap().i32().unwrap().get(1), Some(1));

        let df = aggregator.next().await.unwrap().unwrap().batch.item;
        assert_eq!(df.height(), 2);
        assert_eq!(df.column("value").unwrap().i32().unwrap().get(0), Some(2));
        assert_eq!(df.column("value").unwrap().i32().unwrap().get(1), Some(3));

        // 应该没有更多数据
        assert!(aggregator.next().await.is_none());
    }

    #[tokio::test]
    async fn test_agg() {
        let stream = make_streams(3, Duration::ZERO);

        let mut aggregator = RecordAggregator::builder()
            .stream(stream)
            .batch_size(2)
            .build();

        let df = aggregator.next().await.unwrap().unwrap().batch.item;
        assert_eq!(df.height(), 2);
        assert_eq!(df.column("value").unwrap().i32().unwrap().get(0), Some(0));
        assert_eq!(df.column("value").unwrap().i32().unwrap().get(1), Some(1));

        let df = aggregator.next().await.unwrap().unwrap().batch.item;
        assert_eq!(df.height(), 1);
        assert_eq!(df.column("value").unwrap().i32().unwrap().get(0), Some(2));

        assert!(aggregator.next().await.is_none());
    }

    #[tokio::test(start_paused = true)]
    async fn test_linger() {
        let stream = make_streams(12, Duration::from_millis(100));

        let mut aggregator = RecordAggregator::builder()
            .stream(stream)
            .batch_size(10)
            .linger(Duration::from_millis(500))
            .build();

        let df = aggregator.next().await.unwrap().unwrap().batch.item;
        assert_eq!(df.height(), 5);

        let df = aggregator.next().await.unwrap().unwrap().batch.item;
        assert_eq!(df.height(), 5);

        let df = aggregator.next().await.unwrap().unwrap().batch.item;
        assert_eq!(df.height(), 2);

        assert!(aggregator.next().await.is_none());
    }

    #[tokio::test(start_paused = true)]
    async fn test_linger_exact() {
        let stream = make_streams(10, Duration::from_millis(100));

        let mut aggregator = RecordAggregator::builder()
            .stream(stream)
            .batch_size(10)
            .linger(Duration::from_millis(500))
            .build();

        let df = aggregator.next().await.unwrap().unwrap().batch.item;
        assert_eq!(df.height(), 5);

        let df = aggregator.next().await.unwrap().unwrap().batch.item;
        assert_eq!(df.height(), 5);

        assert!(aggregator.next().await.is_none());
    }

    #[tokio::test(start_paused = true)]
    async fn test_small_linger() {
        let stream = make_streams(3, Duration::from_millis(500));

        let mut aggregator = RecordAggregator::builder()
            .stream(stream)
            .batch_size(10)
            .linger(Duration::from_millis(100))
            .build();

        let df = aggregator.next().await.unwrap().unwrap().batch.item;
        assert_eq!(df.height(), 1);

        let df = aggregator.next().await.unwrap().unwrap().batch.item;
        assert_eq!(df.height(), 1);

        let df = aggregator.next().await.unwrap().unwrap().batch.item;
        assert_eq!(df.height(), 1);

        assert!(aggregator.next().await.is_none());
    }
}
