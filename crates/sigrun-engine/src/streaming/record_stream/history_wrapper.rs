use batch_parquet_reader::decode_task::DecodeFn;
use polars::{
    error::PolarsError,
    frame::DataFrame,
    prelude::{ArithmeticChunked, DataType, IntoColumn, PlSmallStr, TimeUnit},
};

use crate::streaming::{
    record_stream::{AggBatch, WithOffset},
    time_utils::NanoSecs,
};

pub fn wrap_stream<S, F, E, D>(
    s: S,
    extract_timestamp: F,
) -> impl Iterator<Item = impl DecodeFn<Output = AggBatch, Err = E> + Send + 'static>
where
    S: Iterator<Item = D>,
    D: DecodeFn<Output = DataFrame, Err = E> + Send + 'static,
    F: Fn(&mut DataFrame) -> Result<NanoSecs, E> + Send + 'static + Copy,
{
    s.map(move |decode_fn| {
        decode_fn.and_then(move |mut df| {
            let event_time = (extract_timestamp)(&mut df)?;

            let batch = AggBatch {
                batch: WithOffset {
                    offset: 0,
                    item: df,
                },
                event_time,
            };

            Ok(batch)
        })
    })
}

pub fn extract_from_date_and_time(df: &mut DataFrame) -> Result<NanoSecs, PolarsError> {
    let date = df
        .column("date")?
        .cast(&DataType::Datetime(TimeUnit::Nanoseconds, None))?;

    let date = date.datetime()?.physical();

    let time = df.column("time")?.duration()?.physical();

    let timestamp = date
        .wrapping_add(time)
        .into_column()
        .with_name(PlSmallStr::from_static("timestamp"));

    let max = timestamp.max_reduce()?.value().try_extract::<i64>()?;

    let max = NanoSecs::from_nanos(max);

    df.with_column(timestamp)?;

    Ok(max)
}
