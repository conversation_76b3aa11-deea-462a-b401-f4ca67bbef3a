use std::{
    collections::{HashMap, hash_map::Entry},
    sync::Arc,
};

use async_broadcast::{InactiveReceiver, Receiver, broadcast};
use batch_parquet_reader::decode_task::{DecodeFn, Share};
use chrono::{NaiveDate, NaiveTime};
use polars::{error::PolarsError, prelude::PlSmallStr};
use sigrun_io_expr::streaming::StreamType;
use virgo::highfreq::stream::{
    read_high_freq_bar_5s_v2_task, read_high_freq_task, read_order_level_v5_task,
};

use crate::streaming::record_stream::history_wrapper::{extract_from_date_and_time, wrap_stream};

use super::AggBatch;

// TODO: dyn Future in Shared, reduce mem alloc
type WeakRx = InactiveReceiver<Share<AggBatch, PolarsError>>;
type Rx = Receiver<Share<AggBatch, PolarsError>>;

#[derive(Default)]
pub struct StreamManager {
    maps: HashMap<StreamType, WeakRx>,
}

impl StreamManager {
    pub fn get_or_create_history(
        &mut self,
        stream_type: StreamType,
        projection: Option<Arc<[PlSmallStr]>>,
        dates: &[NaiveDate],
        start_time: Option<NaiveTime>,
        end_time: Option<NaiveTime>,
    ) -> Rx {
        let entry = self.maps.entry(stream_type);
        match entry {
            Entry::Occupied(occupied_entry) => occupied_entry.get().activate_cloned(),
            Entry::Vacant(vacant_entry) => {
                let weak =
                    create_history_stream(stream_type, projection, dates, start_time, end_time);
                vacant_entry.insert(weak.clone());
                weak.activate()
            }
        }
    }
}

// 在 filter 之前计算水位线，避免因 filter 导致窗口延迟触发
fn create_history_stream(
    stream_type: StreamType,
    projection: Option<Arc<[PlSmallStr]>>,
    dates: &[NaiveDate],
    start_time: Option<NaiveTime>,
    end_time: Option<NaiveTime>,
) -> WeakRx {
    match stream_type {
        StreamType::OrderLevel => {
            let st = read_order_level_v5_task(dates, start_time, end_time, projection);

            let st = wrap_stream(st, extract_from_date_and_time);

            multiplexer(st.map(|fut| fut.shared()))
        }
        StreamType::Bar => {
            let st = read_high_freq_bar_5s_v2_task(dates, start_time, end_time, projection);

            let st = wrap_stream(st, extract_from_date_and_time);

            multiplexer(st.map(|fut| fut.shared()))
        }
        StreamType::HighFreq(security_type, high_freq_type) => {
            let st = read_high_freq_task(
                security_type,
                high_freq_type,
                dates,
                start_time,
                end_time,
                projection,
            );

            let st = wrap_stream(st, extract_from_date_and_time);

            multiplexer(st.map(|fut| fut.shared()))
        }
    }
}

fn multiplexer<S, T>(source: S) -> InactiveReceiver<T>
where
    S: Iterator<Item = T> + Send + 'static,
    T: Clone + Send + 'static,
{
    let (tx, rx) = broadcast(32);

    tokio::spawn(async move {
        for item in source {
            tx.broadcast_direct(item).await.unwrap();
        }
    });

    rx.deactivate()
}
