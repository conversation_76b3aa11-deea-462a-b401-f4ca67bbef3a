use std::sync::Arc;

use batch_parquet_reader::decode_task::DecodeFn;
use chrono::{NaiveDate, NaiveTime};
use futures_core::stream::BoxStream;
use futures_util::{FutureExt, StreamExt};
use polars::frame::DataFrame;
use polars::prelude::{IntoLazy, PlSmallStr};
use polars::{error::PolarsResult, prelude::Expr};
use sigrun_io_expr::streaming::{IoParam, StreamType, WindowsType};
use tokio::task::spawn_blocking;

use crate::streaming::record_stream::{AggBatch, WithOffset};
use crate::streaming::windows_splitter::split_stream;

use super::record_stream::StreamManager;
use super::watermark_holder::Builder;
use super::windows_splitter::Window;

pub struct StreamBuilder {
    stream_type: StreamType,
    windows_type: WindowsType,
}

impl From<&IoParam> for StreamBuilder {
    fn from(value: &IoParam) -> Self {
        StreamBuilder::new(value.stream_type, value.windows_type)
    }
}

impl StreamBuilder {
    pub fn new(stream_type: StreamType, windows_type: WindowsType) -> Self {
        Self {
            stream_type,
            windows_type,
        }
    }

    #[allow(clippy::too_many_arguments)]
    pub fn build_history(
        self,
        manager: &mut StreamManager,
        builder: &mut Builder,
        all_columns: Option<Arc<[PlSmallStr]>>,
        partial_columns: Option<Arc<[PlSmallStr]>>,
        predicate: Option<Expr>,
        dates: &[NaiveDate],
        start_time: Option<NaiveTime>,
        end_time: Option<NaiveTime>,
    ) -> BoxStream<'static, PolarsResult<Window>> {
        let agg_batch_st = manager.get_or_create_history(
            self.stream_type,
            all_columns,
            dates,
            start_time,
            end_time,
        );

        let filter_fn = build_filter_fn(predicate, partial_columns);

        let agg_batch_st = agg_batch_st.map(move |fut| {
            let filter_fn = filter_fn.clone();
            fut.and_then(move |df| {
                let AggBatch { batch, event_time } = df;

                let df = (filter_fn)(batch.item)?;

                Ok(AggBatch {
                    batch: WithOffset {
                        offset: batch.offset,
                        item: df,
                    },
                    event_time,
                })
            })
        });

        let st = agg_batch_st
            .map(|decode_task| spawn_blocking(|| decode_task.decode()).map(|h| h.unwrap()))
            .buffered(2);

        split_stream(builder, &self.windows_type, st)
    }
}

fn build_filter_fn(
    predicate: Option<Expr>,
    proj: Option<Arc<[PlSmallStr]>>,
) -> Arc<dyn Fn(DataFrame) -> PolarsResult<DataFrame> + Send + Sync + 'static> {
    let apply_predicate = |df: DataFrame, predicate: Expr| df.lazy().filter(predicate).collect();

    let apply_proj = |df: DataFrame, proj: &Arc<[PlSmallStr]>| df.select(proj.iter().cloned());

    match (predicate, proj) {
        (None, None) => Arc::new(Ok),
        (None, Some(proj)) => Arc::new(move |df| apply_proj(df, &proj)),
        (Some(predicate), None) => Arc::new(move |df| apply_predicate(df, predicate.clone())),
        (Some(predicate), Some(proj)) => Arc::new(move |df| {
            let df = apply_proj(df, &proj)?;
            let df = apply_predicate(df, predicate.clone())?;
            Ok(df)
        }),
    }
}
