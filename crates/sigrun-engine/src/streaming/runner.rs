use std::{
    pin::pin,
    sync::{Arc, mpsc},
};

use ahash::HashMap;
use chrono::{NaiveDate, NaiveDateTime, NaiveTime};
use futures_core::{Stream, stream::BoxStream};
use futures_util::StreamExt;
use indexmap::IndexSet;
use itertools::Itertools;
use polars::{
    error::{PolarsError, PolarsResult},
    frame::DataFrame,
    prelude::{AnyValue, Column, DataType, LazyFrame, OptFlags, PlSmallStr, Scalar, concat},
};
use polars_core::POOL;
use polars_expr::state::ExecutionState;
use polars_mem_engine::create_physical_plan;
use polars_plan::{
    plans::{AExpr, IRPlan},
    prelude::{Arena, Node},
};
use polars_python::{error::PyPolarsErr, lazyframe::PyLazyFrame};
use pyo3::{<PERSON><PERSON><PERSON><PERSON>ult, Python};
use sigrun_io_expr::streaming::{Injector, StreamType};
use sigrun_loader::func::{Function, PyLdfWrapper};
use sigrun_parser::{
    iter::node_iter,
    rewrite_scan,
    scan_opt::{ScanOpt, extract_scan, extract_scan_opt},
};

use crate::{
    scan_ref::ScanRef,
    streaming::{matcher::create_matcher, watermark_holder::Builder, windows_splitter::Window},
};

use super::{
    builder::StreamBuilder, matcher::Matched, record_stream::StreamManager,
    watermark_holder::WatermarkHolder,
};

#[derive(Default)]
pub struct Runner {
    manager: StreamManager,
}

impl Runner {
    pub async fn run_history<'py>(
        mut self,
        py: Python<'py>,
        func: Function,
        limit: usize,
        dates: &[NaiveDate],
        start_time: Option<NaiveTime>,
        end_time: Option<NaiveTime>,
    ) -> anyhow::Result<impl Iterator<Item = Result<DataFrame, (NaiveDateTime, PolarsError)>>> {
        let st = self.extract_stream(py, &func, dates, start_time, end_time, limit)?;

        let mut st = pin!(st);

        let (tx, rx) = mpsc::channel();

        while let Some(ret) = st.next().await {
            let merged = ret?;

            run_function(py, &func, merged, tx.clone())?;
        }

        std::mem::drop(tx);

        Ok(rx
            .iter()
            .sorted_by(|a, b| a.0.cmp(&b.0))
            .map(|(dt, result)| result.map_err(|e| (dt, e)))
            .filter(|result| match result {
                Ok(df) => !df.is_empty(),
                Err(_) => true,
            }))
    }

    fn extract_stream<'py>(
        &mut self,
        py: Python<'py>,
        func: &Function,
        dates: &[NaiveDate],
        start_time: Option<NaiveTime>,
        end_time: Option<NaiveTime>,
        limit: usize,
    ) -> anyhow::Result<impl Stream<Item = PolarsResult<Matched>>> {
        let plan = extract_lf(py, func)?
            .with_optimizations(optimize_flag())
            .to_alp_optimized()?;

        let injectors: ProjMapping<'_> = node_iter(&plan)
            .zip_node()
            .filter_map(|(node, ir)| {
                let scan_opt = extract_scan_opt(ir)?;
                let scan_opt =
                    scan_opt.filter_map(|scan| scan.as_any().downcast_ref::<Injector>())?;

                Some((node, scan_opt))
            })
            .collect();

        let unique = injectors.len();

        let mut holder = WatermarkHolder::builder(unique);

        let mut pending = Vec::with_capacity(injectors.len());
        for entry in injectors.mapping.into_values() {
            let st_iter = entry.into_streams(
                &plan.expr_arena,
                &mut self.manager,
                &mut holder,
                dates,
                start_time,
                end_time,
            );

            for st in st_iter {
                pending.push(st);
            }
        }

        let matcher = create_matcher(pending, limit);

        Ok(matcher)
    }
}

fn optimize_flag() -> OptFlags {
    let mut flag = OptFlags::default();

    flag &= !OptFlags::COMM_SUBEXPR_ELIM;
    flag &= !OptFlags::COMM_SUBPLAN_ELIM;

    flag
}

// TODO:
// 1. 执行阶段避免反复优化
// 2. extract_stream 阶段将重复流合并，特别是存在 parametrize 的时候
fn extract_lf<'py>(py: Python<'py>, func: &Function) -> PyResult<LazyFrame> {
    let mut lfs = func
        .run_no_dt(None, py)
        .map_ok(|item: PyLdfWrapper<PyLazyFrame>| item.inner.ldf)
        .collect::<PyResult<Vec<_>>>()?;

    if lfs.len() == 1 {
        Ok(lfs.remove(0))
    } else {
        Ok(concat(lfs, Default::default()).map_err(PyPolarsErr::from)?)
    }
}

fn run_function<'py>(
    py: Python<'py>,
    func: &Function,
    merged: Matched,
    sink: mpsc::Sender<(NaiveDateTime, Result<DataFrame, PolarsError>)>, // TODO: use Sink Trait
) -> anyhow::Result<()> {
    let Matched {
        item: windows,
        event_time: dt,
    } = merged;

    // let dt: NaiveDateTime = dt.into();

    let lf = extract_lf(py, func)?;

    py.allow_threads(move || {
        POOL.spawn(move || {
            let run = || {
                let mut plan = lf.with_optimizations(optimize_flag()).to_alp_optimized()?;

                let top = plan.lp_top;
                rewrite_scan::<Injector>(&mut plan, top);

                // TODO: pass commit handle into injector

                node_iter(&plan)
                    .zip_node()
                    .filter_map(|(node, ir)| extract_scan(ir).map(|scan| (node, scan)))
                    .for_each(|(node, scan)| {
                        if let Some(scan_ref) = ScanRef::<Injector>::new(scan) {
                            let (w, _) = windows.get(&node).unwrap();

                            scan_ref.inject(Ok(w.batch.item.clone()));
                        }
                    });

                let ret = collect(plan);

                std::mem::drop(windows);

                ret
            };

            let ret = run().and_then(|mut df| {
                let len = df.height();
                df.with_column(Column::new_scalar(
                    PlSmallStr::from_static("date"),
                    Scalar::new_date(dt.utc_days() as i32),
                    len,
                ))?;
                df.with_column(Column::new_scalar(
                    PlSmallStr::from_static("time"),
                    Scalar::new(DataType::Time, AnyValue::Time(dt.time().into_nanos())),
                    len,
                ))?;

                Ok(df)
            });

            sink.send((dt.into(), ret)).unwrap();
        });
    });

    Ok(())
}

#[inline]
fn collect(mut plan: IRPlan) -> PolarsResult<DataFrame> {
    let mut physical_plan =
        create_physical_plan(plan.lp_top, &mut plan.lp_arena, &mut plan.expr_arena, None)?;
    let mut state = ExecutionState::default();
    physical_plan.execute(&mut state)
}

#[derive(Default, Debug)]
enum AllColumns {
    PushDown(IndexSet<PlSmallStr, ahash::RandomState>),
    NoPushDown,
    #[default]
    Initial,
}

impl From<AllColumns> for Option<Arc<[PlSmallStr]>> {
    fn from(value: AllColumns) -> Self {
        match value {
            AllColumns::PushDown(columns) => Some(columns.into_iter().collect()),
            AllColumns::NoPushDown => None,
            AllColumns::Initial => None,
        }
    }
}

struct ProjMapping<'a> {
    mapping: HashMap<StreamType, MappingEntry<'a>>,
    len: usize,
}

impl<'a> ProjMapping<'a> {
    fn len(&self) -> usize {
        self.len
    }
}

#[derive(Default)]
struct MappingEntry<'a> {
    nodes: Vec<(Node, ScanOpt<'a, Injector>)>,
    all_columns: AllColumns,
}

impl<'a> MappingEntry<'a> {
    fn push(&mut self, node: Node, scan_opt: ScanOpt<'a, Injector>) {
        match (&scan_opt.proj, &mut self.all_columns) {
            (Some(columns), AllColumns::PushDown(all_columns)) => {
                all_columns.extend(columns.iter().cloned());
            }
            (Some(columns), AllColumns::Initial) => {
                self.all_columns = AllColumns::PushDown(columns.iter().cloned().collect());
            }
            (None, _) => {
                // if lf_idx == 0 && !injector.has_force_columns() {
                //     tracing::warn!(
                //         "Failed to push down projection for {}.\
                //         This may cause performance issue.\
                //         May be you could specify columns manually by force_columns in virgo source.",
                //         injector.params()
                //     );
                // }

                self.all_columns = AllColumns::NoPushDown
            }
            (Some(_), AllColumns::NoPushDown) => (),
        }

        self.nodes.push((node, scan_opt));
    }

    fn into_streams(
        self,
        expr_arena: &Arena<AExpr>,
        manager: &mut StreamManager,
        holder: &mut Builder,
        dates: &[NaiveDate],
        start_time: Option<NaiveTime>,
        end_time: Option<NaiveTime>,
    ) -> impl Iterator<Item = (Node, BoxStream<'static, PolarsResult<Window>>)> {
        macro_rules! chain {
            ($proj:ident, $($col: literal),+) => {{
                $(
                    let paste::paste! {mut [<has_$col>]} = Some(PlSmallStr::from_static($col));
                )+

                $proj.iter().for_each(|proj| match proj.as_str() {
                    $(
                        $col => paste::paste! {[<has_$col>]} = None,
                    )+
                    _ => (),
                });

                let append = [$(paste::paste! {[<has_$col>]}),+].into_iter().flatten();

                $proj.iter().cloned().chain(append).collect::<Arc<[PlSmallStr]>>()
            }};
        }

        let all_columns: Option<Arc<[PlSmallStr]>> = self.all_columns.into();

        let all_columns = all_columns.map(|proj| chain!(proj, "date", "time"));

        self.nodes.into_iter().map(move |(node, scan_opt)| {
            let builder = StreamBuilder::from(scan_opt.scan.params());
            let predicate = scan_opt.predicate.as_ref().map(|ir| ir.to_expr(expr_arena));

            let partical_columns = scan_opt.proj.map(|proj| chain!(proj, "timestamp"));

            let st = builder.build_history(
                manager,
                holder,
                all_columns.clone(),
                partical_columns,
                predicate,
                dates,
                start_time,
                end_time,
            );

            (node, st)
        })
    }
}

impl<'a> FromIterator<(Node, ScanOpt<'a, Injector>)> for ProjMapping<'a> {
    fn from_iter<T: IntoIterator<Item = (Node, ScanOpt<'a, Injector>)>>(iter: T) -> Self {
        let mut mapping: HashMap<StreamType, MappingEntry<'a>> = HashMap::default();

        let mut len = 0;
        for (node, scan_opt) in iter {
            mapping
                .entry(scan_opt.scan.params().stream_type)
                .or_default()
                .push(node, scan_opt);

            len += 1;
        }

        Self { mapping, len }
    }
}
