use std::{
    pin::Pin,
    task::{Context, Poll, ready},
};

use futures_core::Stream;
use pin_project::pin_project;
use polars::{error::PolarsResult, frame::DataFrame};

use crate::streaming::record_stream::AggBatch;

use super::{Window, WindowsSplitter, tumbling_windows::TumblingWindows};

#[pin_project]
pub struct GrowingWindows<ST> {
    #[pin]
    inner: WindowsSplitter<TumblingWindows, ST>,
    acc: DataFrame,
    days: i64,
}

impl<ST> GrowingWindows<ST> {
    pub fn new(stream: WindowsSplitter<TumblingWindows, ST>) -> Self {
        Self {
            inner: stream,
            acc: DataFrame::default(),
            days: -1,
        }
    }
}

impl<ST> Stream for GrowingWindows<ST>
where
    ST: Stream<Item = PolarsResult<AggBatch>>,
{
    type Item = PolarsResult<Window>;

    fn poll_next(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        let this = self.project();

        let mut next_window = match ready!(this.inner.poll_next(cx)) {
            Some(Ok(windows)) => windows,
            Some(Err(e)) => return Poll::Ready(Some(Err(e))),
            None => return Poll::Ready(None),
        };

        let days = next_window.event_time.utc_days();

        if days > *this.days {
            *this.acc = next_window.batch.item;
            *this.days = days;
        } else {
            this.acc.vstack_mut_owned_unchecked(next_window.batch.item);
        };

        // TODO: offset?
        next_window.batch.item = this.acc.clone();
        Poll::Ready(Some(Ok(next_window)))
    }
}
