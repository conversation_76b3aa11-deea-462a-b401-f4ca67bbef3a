use std::collections::BTreeMap;

use polars::{error::PolarsResult, frame::DataFrame};

use crate::streaming::{
    record_stream::{AggBatch, WithOffset},
    time_utils::{NanoSecs, Trunc},
};

use super::{Window, time_bin::truncate};
use super::{WindowsOperator, time_bin::time_bin};

pub struct TumblingWindows {
    pending_windows: BTreeMap<Trunc<NanoSecs>, WithOffset<DataFrame>>,
    width: NanoSecs,
    last_watermark: Trunc<NanoSecs>,
    earlist_time: Trunc<NanoSecs>,
    latest_time: Trunc<NanoSecs>,
}

impl WindowsOperator for TumblingWindows {
    fn process_batch(&mut self, batch: AggBatch) -> PolarsResult<()> {
        let df = batch.batch.item;
        let offset = batch.batch.offset;

        time_bin(df, self.width, Default::default())?
            .into_iter()
            .for_each(|(bin_start, bin)| {
                if bin_start < self.last_watermark {
                    tracing::warn!(
                        "bin_start {} is before watermark {}, dropping {} rows",
                        *bin_start,
                        *self.last_watermark,
                        bin.height()
                    );
                    return;
                }

                let bin_start_date = truncate_nanos(*bin_start, NanoSecs::from_secs(24 * 3600));

                let bin_start = bin_start.clamp(
                    self.earlist_time + bin_start_date,
                    self.latest_time + bin_start_date,
                );

                if let Some(window) = self.pending_windows.get_mut(&bin_start) {
                    window.append(WithOffset::new(offset, bin));
                } else {
                    self.pending_windows
                        .insert(bin_start, WithOffset::new(offset, bin));
                }
            });

        Ok(())
    }

    fn handle_watermark(&mut self, watermark: NanoSecs) -> Option<impl Iterator<Item = Window>> {
        let new_watermark = self.watermark_bin_start(watermark);
        if new_watermark > self.last_watermark {
            self.last_watermark = new_watermark;

            Some(Iter {
                pending_windows: &mut self.pending_windows,
                watermark: self.last_watermark,
                width: self.width,
            })
        } else {
            None
        }
    }

    fn flush(&mut self) -> impl ExactSizeIterator<Item = Window> {
        let pending_windows = std::mem::take(&mut self.pending_windows);
        pending_windows.into_iter().map(|(bin_start, window)| {
            let bin_end = *bin_start + self.width;

            Window {
                batch: window,
                event_time: bin_end,
            }
        })
    }
}

struct Iter<'a> {
    pending_windows: &'a mut BTreeMap<Trunc<NanoSecs>, WithOffset<DataFrame>>,
    watermark: Trunc<NanoSecs>,
    width: NanoSecs,
}

impl<'a> Iterator for Iter<'a> {
    type Item = Window;

    fn next(&mut self) -> Option<Self::Item> {
        self.pending_windows
            .first_entry()
            .filter(|e| *e.key() < self.watermark)
            .map(|e| {
                let (bin_start, batch) = e.remove_entry();
                Window {
                    batch,
                    event_time: *bin_start + self.width,
                }
            })
    }
}

impl TumblingWindows {
    pub fn new(width: NanoSecs, earlist_time: NanoSecs, latest_time: NanoSecs) -> Self {
        Self {
            pending_windows: BTreeMap::new(),
            width,
            last_watermark: Default::default(),
            earlist_time: truncate_nanos(earlist_time, width),
            latest_time: truncate_nanos(latest_time, width),
        }
    }

    fn watermark_bin_start(&self, watermark: NanoSecs) -> Trunc<NanoSecs> {
        truncate_nanos(watermark, self.width)
    }
}

fn truncate_nanos(num: NanoSecs, width: NanoSecs) -> Trunc<NanoSecs> {
    Trunc(NanoSecs::from_nanos(truncate(
        num.into_nanos(),
        width.into_nanos(),
    )))
}

#[cfg(test)]
mod tests {
    use chrono::NaiveDateTime;
    use futures_util::{TryStreamExt, stream};
    use polars::df;

    use crate::streaming::{
        time_utils::{EARLIEST, LATEST},
        watermark_holder::WatermarkHolder,
        windows_splitter::tumbling_windows,
    };

    use super::*;

    #[test]
    fn watermark_bin_start_subsecs_test() {
        let unpined = TumblingWindows::new(NanoSecs::from_secs(5), EARLIEST, LATEST);

        let assert_fn = |input: &str, expected: &str| {
            let watermark: NaiveDateTime = input.parse().unwrap();
            let exceped: NaiveDateTime = expected.parse().unwrap();

            assert_eq!(
                unpined.watermark_bin_start(NanoSecs::from_secs(watermark.and_utc().timestamp())),
                Trunc(NanoSecs::from_secs(exceped.and_utc().timestamp()))
            );
        };

        assert_fn("2024-07-23T10:00:04.900", "2024-07-23T10:00:00");
        assert_fn("2024-07-23T10:00:05", "2024-07-23T10:00:05");
        assert_fn("2024-07-23T10:00:05.100", "2024-07-23T10:00:05");
        assert_fn("2024-07-23T10:00:09.900", "2024-07-23T10:00:05");
        assert_fn("2024-07-23T10:00:10", "2024-07-23T10:00:10");
    }

    async fn split(timestamp_nanos: Vec<Vec<i64>>, width: i64, delay: NanoSecs) -> Vec<Window> {
        let dfs = timestamp_nanos.into_iter().map(|ts| {
            let timestamp: Vec<_> = ts;

            let max = *timestamp.iter().max().unwrap();

            let df = df!(
                "timestamp" => timestamp,
            )
            .unwrap();

            let batch = WithOffset {
                item: df,
                offset: 1,
            };

            Ok(AggBatch {
                batch,
                event_time: NanoSecs::from_nanos(max),
            })
        });

        let mock = stream::iter(dfs);

        let mut builder = WatermarkHolder::builder(1);
        let updater = builder.create_updater();

        let splitter = tumbling_windows(
            mock,
            updater,
            NanoSecs::from_nanos(width),
            EARLIEST,
            LATEST,
            delay,
        );

        splitter.try_collect().await.unwrap()
    }

    #[tokio::test]
    async fn test_tumbling_windows_ordered() {
        let timestamps = vec![vec![10000, 10900, 11000, 11100, 11500]];
        let windows = split(timestamps, 1000, NanoSecs::from_secs(0)).await;

        let timestamps = vec![
            vec![10000],
            vec![10900],
            vec![11000],
            vec![11100],
            vec![11500],
        ];
        let windows1 = split(timestamps, 1000, NanoSecs::from_secs(0)).await;

        let timestamps = vec![vec![10000], vec![10900, 11000], vec![11100], vec![11500]];
        let windows2 = split(timestamps, 1000, NanoSecs::from_secs(0)).await;

        assert_eq!(windows, windows1);
        assert_eq!(windows1, windows2);

        // 第一个窗口：10000-10999
        assert_eq!((&windows[0]).event_time, NanoSecs::from_nanos(11000));
        assert_eq!((&windows[0]).batch.item.height(), 2);

        // 第二个窗口：11000-11999
        assert_eq!((&windows[1]).event_time, NanoSecs::from_nanos(12000));
        assert_eq!((&windows[1]).batch.item.height(), 3);
    }

    // #[tokio::test]
    // async fn test_tumbling_windows_unordered() {
    //     // 乱序, 迟到数据跟水位线在一个batch中
    //     let timestamps = vec![vec![10000], vec![11000], vec![11100, 10900], vec![11500]];
    //     let windows = split(timestamps, 1, Duration::milliseconds(100)).await;
    //     // 乱序但是没有超过水位线
    //     let timestamps = vec![vec![10000, 11000, 10900, 11100, 11500]];
    //     let windows1 = split(timestamps, 1, Duration::milliseconds(100)).await;

    //     // 整个数据都在一个batch中
    //     let timestamps = vec![vec![10000, 11000, 11100, 11500, 10900]];
    //     let windows2 = split(timestamps, 1, Duration::milliseconds(100)).await;
    //     assert_eq!(windows, windows1);
    //     assert_eq!(windows1, windows2);

    //     // 第一个窗口：10000-10999
    //     assert_eq!(
    //         (&windows[0]).event_time,
    //         Utc.timestamp_millis_opt(11000).unwrap().naive_utc()
    //     );
    //     assert_eq!((&windows[0]).batch.item.height(), 2);

    //     // 第二个窗口：11000-11999
    //     assert_eq!(
    //         (&windows[1]).event_time,
    //         Utc.timestamp_millis_opt(12000).unwrap().naive_utc()
    //     );
    //     assert_eq!((&windows[1]).batch.item.height(), 3);
    // }

    // #[tokio::test]
    // async fn test_tumbling_windows_drop() {
    //     // 乱序, 超过水位线，且在不同的batch
    //     let timestamps = vec![
    //         vec![10000],
    //         vec![11000],
    //         vec![11100],
    //         vec![10900],
    //         vec![11500],
    //     ];
    //     let windows = split(timestamps, 1, Duration::milliseconds(100)).await;

    //     let timestamps = vec![vec![10000], vec![11000], vec![11100], vec![11500, 10900]];
    //     let windows1 = split(timestamps, 1, Duration::milliseconds(100)).await;
    //     assert_eq!(windows, windows1);

    //     // 第一个窗口：10000-10999
    //     assert_eq!(
    //         (&windows[0]).event_time,
    //         Utc.timestamp_millis_opt(11000).unwrap().naive_utc()
    //     );
    //     assert_eq!((&windows[0]).batch.item.height(), 1);

    //     // 第二个窗口：11000-11999
    //     assert_eq!(
    //         (&windows[1]).event_time,
    //         Utc.timestamp_millis_opt(12000).unwrap().naive_utc()
    //     );
    //     assert_eq!((&windows[1]).batch.item.height(), 3);
    // }

    // #[tokio::test]
    // async fn test_tumbling_windows_big_delay() {
    //     // 乱序, 超过水位线，且在不同的batch
    // }
}
