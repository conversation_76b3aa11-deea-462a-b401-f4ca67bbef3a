use std::{
    collections::VecDeque,
    pin::Pin,
    task::{Context, Poll},
    time::Duration,
};

use chrono::NaiveTime;
use futures_core::{Stream, stream::BoxStream};
use pin_project::pin_project;
use polars::{error::PolarsResult, frame::DataFrame};
use sigrun_io_expr::streaming::WindowsType;
use tokio::time::{Interval, interval};

use crate::streaming::{
    watermark_holder::{Builder, Updater},
    windows_splitter::{growing_windows::GrowingWindows, tumbling_windows::TumblingWindows},
};

use super::{
    record_stream::{AggBatch, WithOffset},
    time_utils::NanoSecs,
};

mod growing_windows;
mod time_bin;
mod tumbling_windows;

pub fn split_stream(
    builder: &mut Builder,
    windows: &WindowsType,
    input: impl Stream<Item = PolarsResult<AggBatch>> + Send + 'static,
) -> BoxStream<'static, PolarsResult<Window>> {
    let updater = builder.create_updater();
    match windows {
        WindowsType::Dummy => panic!("removed"),
        WindowsType::Growing {
            width,
            earliest,
            latest,
        } => {
            let inner = tumbling_windows(
                input,
                updater,
                (*width).into(),
                (*earliest - NaiveTime::MIN).into(),
                (*latest - NaiveTime::MIN).into(),
                NanoSecs::from_secs(3),
            );

            Box::pin(GrowingWindows::new(inner))
        }
        WindowsType::Tumbling {
            width,
            earliest,
            latest,
        } => Box::pin(tumbling_windows(
            input,
            updater,
            (*width).into(),
            (*earliest - NaiveTime::MIN).into(),
            (*latest - NaiveTime::MIN).into(),
            NanoSecs::from_secs(3),
        )),
    }
}

#[pin_project]
struct WindowsSplitter<O, ST> {
    #[pin]
    stream: ST,
    operator: O,
    ready_windows: VecDeque<Window>,
    end: bool,
    tick: Interval,
    updater: Updater,
    delay: NanoSecs,
}

fn tumbling_windows<ST>(
    stream: ST,
    updater: Updater,
    width: NanoSecs,
    earlist: NanoSecs,
    latest: NanoSecs,
    delay: NanoSecs,
) -> WindowsSplitter<TumblingWindows, ST> {
    WindowsSplitter {
        stream,
        operator: TumblingWindows::new(width, earlist, latest),
        ready_windows: Default::default(),
        end: false,
        tick: interval(Duration::from_millis(200)),
        updater,
        delay,
    }
}

impl<O, ST> Stream for WindowsSplitter<O, ST>
where
    O: WindowsOperator,
    ST: Stream<Item = PolarsResult<AggBatch>>,
{
    type Item = PolarsResult<Window>;

    fn poll_next(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        let mut this = self.project();

        loop {
            if let Some(window) = this.ready_windows.pop_front() {
                return Poll::Ready(Some(Ok(window)));
            }

            if *this.end {
                return Poll::Ready(None);
            }

            'poll: loop {
                let poll_stream = this.stream.as_mut().poll_next(cx);
                let poll_tick = this.tick.poll_tick(cx);

                if poll_stream.is_pending() && poll_tick.is_pending() {
                    return Poll::Pending;
                }

                if let Poll::Ready(poll_stream) = poll_stream {
                    let batch = match poll_stream {
                        Some(Ok(batch)) => batch,
                        Some(Err(e)) => return Poll::Ready(Some(Err(e))),
                        None => {
                            this.ready_windows.extend(this.operator.flush());
                            *this.end = true;
                            break 'poll;
                        }
                    };

                    let event_time = batch.event_time;

                    if let Err(e) = this.operator.process_batch(batch) {
                        return Poll::Ready(Some(Err(e)));
                    }

                    this.updater.update(event_time - *this.delay);
                }

                if poll_tick.is_ready() {
                    let readys = this
                        .operator
                        .handle_watermark(this.updater.last_present_watermark());

                    if let Some(readys) = readys {
                        this.ready_windows.extend(readys);
                        break 'poll;
                    }
                }
            }
        }
    }
}

trait WindowsOperator {
    fn process_batch(&mut self, batch: AggBatch) -> PolarsResult<()>;

    fn handle_watermark(&mut self, watermark: NanoSecs) -> Option<impl Iterator<Item = Window>>;

    fn flush(&mut self) -> impl ExactSizeIterator<Item = Window>;
}

#[derive(Clone, Debug)]
pub struct Window {
    pub batch: WithOffset<DataFrame>,
    pub event_time: NanoSecs,
}

impl PartialEq for Window {
    fn eq(&self, other: &Self) -> bool {
        self.batch.item == other.batch.item && self.event_time == other.event_time
    }
}
