use polars::{
    error::Polars<PERSON><PERSON>ult,
    frame::<PERSON><PERSON><PERSON><PERSON>,
    prelude::{<PERSON><PERSON><PERSON><PERSON>hunked, GroupsIndicator, Int64Chunked, IntoColumn, arity::unary_mut_values},
};
use polars_core::utils::arrow::bitmap::Bitmap;
use rayon::iter::ParallelIterator;

use crate::streaming::time_utils::{NanoSecs, Trunc};

pub fn time_bin(
    mut batch: DataFrame,
    width: NanoSecs,
    interval: NanoSecs,
) -> PolarsResult<Vec<(Trunc<NanoSecs>, DataFrame)>> {
    let dt = batch
        .drop_in_place("timestamp")?
        .take_materialized_series()
        .i64()?
        .clone();

    let ever = width + interval;

    if interval > NanoSecs::from_secs(0) {
        batch = filter_by_width(batch, &dt, width, ever)?
    };

    let truncted = dt.apply_in_place(|t| truncate(t, ever.into_nanos()));

    let by = truncted.clone().into_column();

    // TODO: benchmark:
    // 1. use par_iter
    // 2. use iter and remove collect
    polars_core::POOL.install(|| {
        Ok(batch
            .group_by_with_series(vec![by], true, true)?
            .take_groups()
            .par_iter()
            .map(|g| {
                let group = unsafe { take_df_unchecked(&batch, &g) };
                let key_value = unsafe { take_first_unchecked(&truncted, g) };
                (key_value, group)
            })
            .collect::<Vec<_>>())
    })
}

fn filter_by_width(
    batch: DataFrame,
    by: &Int64Chunked,
    width: NanoSecs,
    ever: NanoSecs,
) -> PolarsResult<DataFrame> {
    let mask: BooleanChunked = unary_mut_values(&by, |arr| {
        let bitmap: Bitmap = arr
            .values()
            .iter()
            .map(|v| {
                let bucket = truncate(*v, ever.into_nanos());
                *v <= bucket + width.into_nanos()
            })
            .collect();
        bitmap.into()
    });

    batch.filter(&mask)
}

#[inline(always)]
pub fn truncate(t: i64, every: i64) -> i64 {
    let remainder = t % every;
    t - (remainder + every * (remainder < 0) as i64)

    // (t / every) * every
}

unsafe fn take_df_unchecked(df: &DataFrame, g: &GroupsIndicator) -> DataFrame {
    match g {
        GroupsIndicator::Idx(idx) => unsafe { df.take_slice_unchecked(idx.1) },
        GroupsIndicator::Slice([first, len]) => df.slice(*first as i64, *len as usize),
    }
}

unsafe fn take_first_unchecked(
    truncted_column: &Int64Chunked,
    g: GroupsIndicator,
) -> Trunc<NanoSecs> {
    let first = g.first();
    let phys = unsafe { truncted_column.value_unchecked(first as usize) };
    Trunc(NanoSecs::from_nanos(phys))
}

#[cfg(test)]
mod tests {
    use super::*;
    use polars::prelude::*;

    #[test]
    fn test_split_windows_milliseconds() {
        // 创建测试数据：每100毫秒一个时间戳
        let df = df!("timestamp" => [1000i64,1050,1100,1150]).unwrap();

        let every = NanoSecs::from_nanos(100);

        let result = time_bin(df, every, Default::default()).unwrap();

        // 应该分成两个窗口：1000-1099 和 1100-1199
        assert_eq!(result.len(), 2);

        // 检查第一个窗口
        let (first_time, first_df) = &result[0];
        assert_eq!(first_time.into_nanos(), 1000);
        assert_eq!(first_df.height(), 2);

        // 检查第二个窗口
        let (second_time, second_df) = &result[1];
        assert_eq!(second_time.into_nanos(), 1100);
        assert_eq!(second_df.height(), 2);
    }

    #[test]
    fn test_split_windows_empty_dataframe() {
        let timestamps: [i64; 0] = [];
        let df = df!("timestamp" => timestamps).unwrap();
        let every = NanoSecs::from_secs(100);

        let result = time_bin(df, every, Default::default()).unwrap();
        assert_eq!(result.len(), 0);
    }
}
