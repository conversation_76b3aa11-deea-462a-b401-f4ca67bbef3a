use std::{
    fmt::Display,
    ops::{<PERSON>d, <PERSON><PERSON>, <PERSON>},
};

use chrono::{DateTime, NaiveDateTime, TimeDelta, Utc};

pub const EARLIEST: NanoSecs = NanoSecs::from_secs(0);

pub const LATEST: NanoSecs = NanoSecs::from_nanos(24 * 3600 * 1_000_000_000 - 1);

// 由于 WatermarkHolder 中要使用原子变量，所以不使用 NaiveDateTime
// NanoSecs 必须大于等于 0。但是 NaiveDateTime 要的是 i64
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Default)]
pub struct NanoSecs(i64);

impl From<TimeDelta> for NanoSecs {
    fn from(value: TimeDelta) -> Self {
        Self(value.num_nanoseconds().unwrap())
    }
}

impl From<NanoSecs> for NaiveDateTime {
    fn from(value: NanoSecs) -> Self {
        DateTime::<Utc>::from_timestamp_nanos(value.0).naive_utc()
    }
}

impl NanoSecs {
    #[inline]
    pub fn utc_days(self) -> i64 {
        self.into_secs() / (3600 * 24)
    }

    pub fn time(self) -> NanoSecs {
        Self(self.0 % (3600 * 24 * 1_000_000_000))
    }

    #[inline]
    pub const fn from_secs(secs: i64) -> Self {
        Self(secs * 1_000_000_000)
    }

    #[inline]
    pub const fn from_millis(millis: i64) -> Self {
        Self(millis * 1_000_000)
    }

    #[inline]
    pub const fn from_micros(micros: i64) -> Self {
        Self(micros * 1_000)
    }

    #[inline]
    pub const fn from_nanos(nanos: i64) -> Self {
        Self(nanos)
    }

    #[inline]
    pub const fn into_nanos(self) -> i64 {
        self.0
    }

    #[inline]
    pub const fn into_micors(self) -> i64 {
        self.0 / 1_000
    }

    #[inline]
    pub const fn into_millis(self) -> i64 {
        self.0 / 1_000_000
    }

    #[inline]
    pub const fn into_secs(self) -> i64 {
        self.0 / 1_000_000_000
    }
}

impl Display for NanoSecs {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", DateTime::<Utc>::from_timestamp_nanos(self.0))
    }
}

impl Add for NanoSecs {
    type Output = NanoSecs;

    fn add(self, rhs: Self) -> Self::Output {
        Self(self.0 + rhs.0)
    }
}

impl Sub for NanoSecs {
    type Output = NanoSecs;

    fn sub(self, rhs: Self) -> Self::Output {
        Self(self.0 - rhs.0)
    }
}

#[derive(Default, Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub struct Trunc<T>(pub T);

impl<T: Add> Add for Trunc<T> {
    type Output = Trunc<T::Output>;

    fn add(self, rhs: Self) -> Self::Output {
        Trunc(self.0 + rhs.0)
    }
}

impl<T> Deref for Trunc<T> {
    type Target = T;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}
