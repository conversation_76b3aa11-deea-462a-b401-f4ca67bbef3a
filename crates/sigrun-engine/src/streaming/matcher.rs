use std::{
    collections::BTreeMap,
    pin::{Pin, pin},
    sync::Arc,
    task::{Context, Poll, ready},
};

use ahash::HashMap;
use futures_core::Stream;
use futures_util::stream::StreamExt;
use pin_project::pin_project;
use polars_plan::prelude::Node;
use tokio::sync::{OwnedSemaphorePermit, Semaphore, mpsc};

use super::{time_utils::NanoSecs, windows_splitter::Window};

pub fn create_matcher<I, S, E>(streams: I, limit: usize) -> MatchStream<E>
where
    I: IntoIterator<Item = (Node, S)>,
    I::IntoIter: ExactSizeIterator,
    S: Stream<Item = Result<Window, E>> + Unpin + Send + 'static,
    E: Send + 'static,
{
    let iter = streams.into_iter();

    let pending = Pending::new(iter.len());

    let (tx, rx) = mpsc::channel(32);

    for (node, st) in iter {
        let tx = tx.clone();
        let fut = async move {
            let mut st = pin!(st);
            let permit = Arc::new(Semaphore::new(limit));

            while let Some(item) = st.next().await {
                let track = permit.clone().acquire_owned().await.unwrap();

                let unmatched = UnMatched { node, item, track };
                if tx.send(unmatched).await.is_err() {
                    break;
                }
            }
        };
        tokio::spawn(fut);
    }

    MatchStream {
        streams: rx,
        pending,
    }
}

pub struct Matched {
    pub item: HashMap<Node, (Window, OwnedSemaphorePermit)>,
    pub event_time: NanoSecs,
}

pub struct UnMatched<E> {
    node: Node,
    item: Result<Window, E>,
    track: OwnedSemaphorePermit,
}

#[pin_project]
pub struct MatchStream<E> {
    #[pin]
    streams: mpsc::Receiver<UnMatched<E>>,
    pending: Pending,
}

struct Pending {
    expected_len: usize,
    watermark: NanoSecs,
    queues: BTreeMap<NanoSecs, HashMap<Node, (Window, OwnedSemaphorePermit)>>,
}

impl Pending {
    fn new(expected_len: usize) -> Self {
        Self {
            expected_len,
            watermark: NanoSecs::default(),
            queues: BTreeMap::default(),
        }
    }

    fn push(&mut self, key: Node, item: Window, track: OwnedSemaphorePermit) {
        let event_time = item.event_time;
        let queue = self.queues.entry(event_time).or_default();

        debug_assert!(queue.len() < self.expected_len);

        queue.insert(key, (item, track));
        if queue.len() == self.expected_len {
            self.watermark = self.watermark.max(event_time);
        }
    }

    fn try_pop(&mut self) -> Option<Matched> {
        loop {
            let f = self.queues.first_entry()?;

            if f.key() > &self.watermark {
                return None;
            }

            let (event_time, bucket) = f.remove_entry();

            if bucket.len() == self.expected_len {
                return Some(Matched {
                    item: bucket,
                    event_time,
                });
            }
        }
    }
}

impl<E> Stream for MatchStream<E> {
    type Item = Result<Matched, E>;

    fn poll_next(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        let mut this = self.project();

        loop {
            if let Some(ret) = this.pending.try_pop() {
                return Poll::Ready(Some(Ok(ret)));
            }

            let Some(un_merge) = ready!(this.streams.poll_recv(cx)) else {
                return Poll::Ready(None);
            };

            let (key, item, track) = match un_merge.item {
                Ok(item) => (un_merge.node, item, un_merge.track),
                Err(e) => {
                    return Poll::Ready(Some(Err(e)));
                }
            };

            this.pending.push(key, item, track);
        }
    }
}
