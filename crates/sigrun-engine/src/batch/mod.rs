use std::{
    collections::BTreeMap,
    hash::Hash,
    sync::{Arc, OnceLock, mpsc::Sender},
};

use ahash::{HashMap, HashSet};
use chrono::NaiveDateTime;
use indexmap::IndexSet;
use itertools::Itertools;
use polars::prelude::*;
use polars_core::POOL;
use polars_expr::state::ExecutionState;
use polars_mem_engine::create_physical_plan;
use polars_plan::{
    plans::{IR, IRPlan, expr_ir::ExprIR},
    prelude::Node,
};
use polars_utils::unique_id::UniqueId;
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use runtime::current;
use sigrun_io_expr::batch::{Injector, IoParam};
use sigrun_parser::rewrite_scan;
use sigrun_utils::{convert::cast_df, env::io_engine_enabled, r#override::Override};
use smallvec::SmallVec;
use tokio::sync::{OwnedSemaphorePermit, Semaphore, mpsc, oneshot};
use tracing::{Instrument, Span, debug_span, info_span};
use virgo::table::Virgo;

use crate::scan_ref::ScanRef;

#[derive(PartialEq, Eq, Hash, Debug)]
struct Io {
    param: IoParam,
    predicate: Option<Expr>,
}

impl Io {
    async fn execute(
        self,
        virgo: Virgo,
        columns: Option<&[PlSmallStr]>,
    ) -> anyhow::Result<DataFrame> {
        match self.param {
            IoParam::Table {
                name,
                mut opt,
                timeout,
                schema,
            } => {
                let opt = if let Some(force_columns) = opt.remove("force_columns") {
                    opt.insert("columns".to_string(), force_columns);
                    serde_json::to_string(&opt)?
                } else if let Some(columns) = columns {
                    serde_json::to_string(&Override::new(&opt, columns))?
                } else {
                    serde_json::to_string(&opt)?
                };

                debug_assert!(self.predicate.is_none());

                let df = virgo.read(name, opt, timeout).await?;

                cast_df(df, &schema).map_err(Into::into)
            }
            IoParam::HighFreq { subplan, param, .. } => {
                let span = info_span!("highfreq", param = display(param));
                span.in_scope(|| {
                    let mut lf = if let Some(columns) = columns {
                        subplan.select(columns.iter().map(|c| col(c.clone())).collect::<Vec<_>>())
                    } else {
                        *subplan
                    };

                    if let Some(predicate) = self.predicate {
                        lf = lf.filter(predicate);
                    }

                    tokio::task::block_in_place(|| {
                        POOL.install(|| lf.collect().map_err(Into::into))
                    })
                })
            }
        }
    }
}

#[derive(Default, Debug)]
enum AllColumns {
    PushDown(IndexSet<PlSmallStr, ahash::RandomState>),
    NoPushDown,
    #[default]
    Initial,
}

#[derive(Default)]
struct IoEntry {
    all_columns: AllColumns,
    injects: Vec<ScanRef<Injector>>,
    in_neighbor: HashSet<CacheKey>,
}

impl core::fmt::Debug for IoEntry {
    fn fmt(&self, f: &mut core::fmt::Formatter) -> core::fmt::Result {
        let IoEntry {
            all_columns,
            injects,
            in_neighbor,
        } = self;
        f.debug_struct("Entry")
            .field("all_columns", &all_columns)
            .field("injects cnt", &injects.len())
            .field("in_neighbor", &in_neighbor)
            .finish()
    }
}

pub struct Plan<T = IRPlan> {
    pub inner: T,
    pub dt: NaiveDateTime,
    pub func_idx: FuncIdx,
}

#[derive(Eq, Debug, Clone, Copy, Hash, PartialEq)]
struct CacheKey {
    lf_idx: usize,
    cache_node: Option<UniqueId>,
}

struct CacheEntry {
    out_degree: usize,
    in_neighbor: HashSet<CacheKey>,
    hit_cnt: u32,
    plan: IRPlan,
    state: ExecutionState,
    dt: NaiveDateTime,
    fn_idx: FuncIdx,
}

impl std::fmt::Debug for CacheEntry {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let alertnate = f.alternate();
        let mut builder = f.debug_struct("CacheEntry");

        builder
            .field("cnt", &self.out_degree)
            .field("in_neighbors", &self.in_neighbor);

        if alertnate {
            builder.field("plan", &self.plan);
        }

        builder.finish()
    }
}

struct Asset {
    fn_idx: FuncIdx,
    order: usize,
    result: Result<DataFrame, (NaiveDateTime, PolarsError)>,
}

pub struct Register {
    cache_graph: HashMap<CacheKey, CacheEntry>,
    io: HashMap<Io, (IoEntry, usize)>,
    io_cnt: usize,
}

impl std::fmt::Debug for Register {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("Register")
            .field("cache_graph", &self.cache_graph)
            .field("io", &self.io)
            .finish()
    }
}

#[derive(Hash, PartialEq, Eq, Debug, Clone, Copy)]
pub struct FuncIdx(usize);

impl From<usize> for FuncIdx {
    fn from(value: usize) -> Self {
        Self(value)
    }
}

impl Register {
    pub fn new(inputs: Vec<Plan<LazyFrame>>) -> PolarsResult<Self> {
        let optimized = POOL.install(|| {
            inputs
                .into_par_iter()
                .map(|plan| {
                    plan.inner
                        .with_optimizations(opt_state())
                        .to_alp_optimized()
                        .map(|lf| Plan {
                            dt: plan.dt,
                            inner: lf,
                            func_idx: plan.func_idx,
                        })
                })
                .collect::<PolarsResult<Vec<_>>>()
        })?;

        let mut register = Register {
            cache_graph: Default::default(),
            io: Default::default(),
            io_cnt: 0,
        };

        let in_sigrun = io_engine_enabled();

        for (lf_idx, mut optimized_plan) in optimized.into_iter().enumerate() {
            let top_node = optimized_plan.inner.lp_top;

            rewrite_scan::<Injector>(&mut optimized_plan.inner, top_node);

            let state = ExecutionState::new();

            let root_key = CacheKey {
                lf_idx,
                cache_node: None,
            };

            register
                .cache_graph
                .entry(root_key)
                .or_insert_with(|| CacheEntry {
                    out_degree: 0,
                    hit_cnt: 0,
                    in_neighbor: Default::default(),
                    state: state.split(),
                    plan: IRPlan {
                        lp_top: top_node,
                        lp_arena: optimized_plan.inner.lp_arena.clone(),
                        expr_arena: optimized_plan.inner.expr_arena.clone(),
                    },
                    dt: optimized_plan.dt,
                    fn_idx: optimized_plan.func_idx,
                });

            if in_sigrun {
                let cnt =
                    register.build_register(&optimized_plan, top_node, root_key, lf_idx, &state);
                register.io_cnt += cnt;
            }

            std::mem::drop(state);
        }

        Ok(register)
    }

    pub fn run(
        mut self,
        virgo: Virgo,
        limit: usize,
        len: usize,
        on_recv: impl Fn(),
    ) -> impl Iterator<Item = (Result<DataFrame, (NaiveDateTime, PolarsError)>, FuncIdx)> {
        let (tx, rx) = std::sync::mpsc::channel();
        let limit = Arc::new(Semaphore::new(limit));

        let (io_tx, mut io_rx) = mpsc::unbounded_channel();

        if self.io_cnt != 0 {
            io_task(self.io, virgo, io_tx.clone(), limit.clone());
        }

        root_task(&mut self.cache_graph, tx.clone(), io_tx.clone(), limit);

        let (exit_tx, mut exit_rx) = oneshot::channel::<()>();

        let span = info_span!("run lazyframe");

        current().spawn(
            async move {
                loop {
                    tokio::select! {
                        biased; Some(keys) = io_rx.recv() => {
                            for key in keys.into_iter() {
                                handle_key(&mut self.cache_graph, key, tx.clone(), io_tx.clone());
                            }
                        }
                        _ = &mut exit_rx => {
                            break;
                        },
                    }
                }
            }
            .instrument(span),
        );

        let ret = rx
            .iter()
            .take(len)
            .inspect(|_| on_recv())
            .sorted_by(|a, b| a.order.cmp(&b.order))
            .map(|asset| (asset.result, asset.fn_idx))
            .filter(|(result, _)| match result {
                Ok(df) => !df.is_empty(),
                Err(_) => true,
            });

        let _ = exit_tx.send(());
        ret
    }

    fn build_register(
        &mut self,
        plan: &Plan,
        node: Node,
        in_neighbor: CacheKey,
        lf_idx: usize,
        state: &ExecutionState,
    ) -> usize {
        let ir = unsafe { plan.inner.lp_arena.get_unchecked(node) };
        let inputs = ir.get_inputs();
        if inputs.is_empty() {
            if let IR::Scan {
                unified_scan_args,
                predicate,
                scan_type,
                ..
            } = ir
            {
                let function = match &**scan_type {
                    FileScanIR::Anonymous { function, .. } => function,
                    _ => return 0,
                };
                return self.process_scan(
                    &plan.inner,
                    lf_idx,
                    unified_scan_args,
                    predicate,
                    function,
                    in_neighbor,
                );
            }
        }

        let mut scan_cnt = 0;
        for node in inputs.iter() {
            let input_expr = unsafe { plan.inner.lp_arena.get_unchecked(*node) };

            match input_expr {
                IR::Scan {
                    unified_scan_args,
                    predicate,
                    scan_type,
                    ..
                } => {
                    let function = match &**scan_type {
                        FileScanIR::Anonymous { function, .. } => function,
                        _ => continue,
                    };
                    scan_cnt += self.process_scan(
                        &plan.inner,
                        lf_idx,
                        unified_scan_args,
                        predicate,
                        function,
                        in_neighbor,
                    );
                }
                IR::Cache { input, id, .. } => {
                    let cache_key = CacheKey {
                        lf_idx,
                        cache_node: Some(*id),
                    };

                    self.process_cache(cache_key, plan, *input, in_neighbor, state);

                    scan_cnt += self.build_register(plan, *input, cache_key, lf_idx, state);
                }
                _ => {
                    scan_cnt += self.build_register(plan, *node, in_neighbor, lf_idx, state);
                }
            }
        }

        scan_cnt
    }

    fn process_cache(
        &mut self,
        cache_key: CacheKey,
        plan: &Plan,
        input: Node,
        in_neighbor: CacheKey,
        state: &ExecutionState,
    ) {
        let entry = self
            .cache_graph
            .entry(cache_key)
            .or_insert_with(|| CacheEntry {
                out_degree: 0,
                hit_cnt: 0,
                in_neighbor: Default::default(),
                state: state.split(),
                plan: IRPlan {
                    lp_top: input,
                    lp_arena: plan.inner.lp_arena.clone(),
                    expr_arena: plan.inner.expr_arena.clone(),
                },
                dt: plan.dt,
                fn_idx: plan.func_idx,
            });

        entry.hit_cnt += 1;

        if entry.in_neighbor.insert(in_neighbor) {
            if let Some(entry) = self.cache_graph.get_mut(&in_neighbor) {
                entry.out_degree += 1;
            }
        }
    }

    fn process_scan(
        &mut self,
        plan: &IRPlan,
        lf_idx: usize,
        unified_scan_args: &UnifiedScanArgs,
        predicate: &Option<ExprIR>,
        function: &Arc<dyn AnonymousScan>,
        in_neighbor: CacheKey,
    ) -> usize {
        let Some(injector) = ScanRef::<Injector>::new(function) else {
            return 0;
        };

        let filter = predicate.as_ref().map(|f| f.to_expr(&plan.expr_arena));

        let key = Io {
            param: injector.params().clone(),
            predicate: filter,
        };

        let tuple = self
            .io
            .entry(key)
            .or_insert_with(|| (Default::default(), lf_idx));

        let entry = &mut tuple.0;

        match (&unified_scan_args.projection, &mut entry.all_columns) {
            (Some(columns), AllColumns::PushDown(all_columns)) => {
                all_columns.extend(columns.iter().cloned());
            }
            (Some(columns), AllColumns::Initial) => {
                entry.all_columns = AllColumns::PushDown(columns.iter().cloned().collect());
            }
            (None, _) => {
                if lf_idx == 0 && !injector.has_force_columns() {
                    tracing::warn!(
                        "Failed to push down projection for {}.\
                        This may cause performance issue.\
                        May be you could specify columns manually by force_columns in virgo source.",
                        injector.params()
                    );
                }

                entry.all_columns = AllColumns::NoPushDown
            }
            (Some(_), AllColumns::NoPushDown) => (),
        }

        if entry.in_neighbor.insert(in_neighbor) {
            if let Some(entry) = self.cache_graph.get_mut(&in_neighbor) {
                entry.out_degree += 1;
            }
        }

        entry.injects.push(injector);

        1
    }
}

struct TrackTask {
    keys: HashSet<CacheKey>,
    permit: Arc<OwnedSemaphorePermit>,
}

impl TrackTask {
    fn into_iter(self) -> impl Iterator<Item = (CacheKey, Arc<OwnedSemaphorePermit>)> {
        self.keys
            .into_iter()
            .map(move |key| (key, self.permit.clone()))
    }
}

#[allow(clippy::mutable_key_type)]
fn io_task(
    table: HashMap<Io, (IoEntry, usize)>,
    virgo: Virgo,
    tx: mpsc::UnboundedSender<TrackTask>,
    limit: Arc<Semaphore>,
) {
    // TODO: benchmark vec and smallvec
    let mut grouped_table: BTreeMap<usize, SmallVec<[(Io, IoEntry); 1]>> = BTreeMap::new();

    for (key, (entry, idx)) in table {
        let v = grouped_table.entry(idx).or_default();
        v.push((key, entry));
    }

    let schedule_io_task_span = info_span!("schedule io task");
    current().spawn(
        async move {
            for lf in grouped_table.into_values() {
                let permit = Arc::new(limit.clone().acquire_owned().await.unwrap());

                for (expr, deps) in lf {
                    let virgo = virgo.clone();
                    let tx = tx.clone();

                    let permit = permit.clone();
                    current().spawn(
                        async move {
                            let all_columns: Option<Vec<PlSmallStr>> = match deps.all_columns {
                                AllColumns::PushDown(columns) => Some(columns.into_vec()),
                                AllColumns::NoPushDown => None,
                                AllColumns::Initial => None,
                            };

                            let ret = expr
                                .execute(virgo, all_columns.as_deref())
                                .await
                                .map_err(Arc::new);

                            for inject in deps.injects {
                                inject.inject(ret.clone());
                            }

                            if !deps.in_neighbor.is_empty() {
                                let _ = tx.send(TrackTask {
                                    keys: deps.in_neighbor,
                                    permit,
                                });
                            }
                        }
                        .in_current_span(),
                    );
                }
            }
        }
        .instrument(schedule_io_task_span),
    );
}

fn root_task(
    cache_graph: &mut HashMap<CacheKey, CacheEntry>,
    final_tx: Sender<Asset>,
    io_tx: mpsc::UnboundedSender<TrackTask>,
    limit: Arc<Semaphore>,
) {
    let root_task: Vec<_> = cache_graph.extract_if(|_, v| v.out_degree == 0).collect();
    if root_task.is_empty() {
        return;
    }

    current().spawn(async move {
        for (key, entry) in root_task {
            let permit = limit.clone().acquire_owned().await.unwrap();

            execute_entry(key, entry, final_tx.clone(), io_tx.clone(), permit.into());
        }
    });
}

fn handle_key(
    cache_graph: &mut HashMap<CacheKey, CacheEntry>,
    key: (CacheKey, Arc<OwnedSemaphorePermit>),
    final_tx: Sender<Asset>,
    io_tx: mpsc::UnboundedSender<TrackTask>,
) {
    let (key, permit) = key;

    let entry = cache_graph.get_mut(&key).unwrap();
    if entry.out_degree > 1 {
        entry.out_degree -= 1;
        return;
    }
    let entry = cache_graph.remove(&key).unwrap();

    execute_entry(key, entry, final_tx, io_tx, permit);
}

fn execute_entry(
    key: CacheKey,
    mut entry: CacheEntry,
    final_tx: Sender<Asset>,
    io_tx: mpsc::UnboundedSender<TrackTask>,
    permit: Arc<OwnedSemaphorePermit>,
) {
    let span = Span::current();
    POOL.spawn(move || {
        if let Some(cache_node) = key.cache_node {
            debug_span!(parent:span, "execute cache").in_scope(|| {
                let df = match collect(entry.plan, &mut entry.state) {
                    Ok(df) => df,
                    Err(e) => {
                        let _ = final_tx.send(Asset {
                            order: key.lf_idx,
                            result: Err((entry.dt, e)),
                            fn_idx: entry.fn_idx,
                        });
                        return;
                    }
                };

                let _ = entry
                    .state
                    .get_df_cache(&cache_node, entry.hit_cnt)
                    .1
                    .set(df);

                std::mem::drop(entry.state);

                let _ = io_tx.send(TrackTask {
                    keys: entry.in_neighbor,
                    permit,
                });
            });
        } else {
            debug_span!(parent:span, "execute lazyframe").in_scope(|| {
                let df = match collect(entry.plan, &mut entry.state) {
                    Ok(df) => df,
                    Err(e) => {
                        let _ = final_tx.send(Asset {
                            order: key.lf_idx,
                            result: Err((entry.dt, e)),
                            fn_idx: entry.fn_idx,
                        });
                        return;
                    }
                };

                std::mem::drop(permit);

                let _ = final_tx.send(Asset {
                    order: key.lf_idx,
                    result: Ok(df),
                    fn_idx: entry.fn_idx,
                });
            })
        }
    });
}

#[inline]
fn collect(mut plan: IRPlan, state: &mut ExecutionState) -> PolarsResult<DataFrame> {
    let mut physical_plan = create_physical_plan(
        plan.lp_top,
        &mut plan.lp_arena,
        &mut plan.expr_arena,
        BUILD_STREAMING_EXECUTOR,
    )?;
    physical_plan.execute(state)
}

fn opt_state() -> OptFlags {
    static LOG: OnceLock<()> = OnceLock::new();
    let mut flags = OptFlags::default();
    if std::env::var("DISABLE_COMM_SUBPLAN_ELIM").unwrap_or_default() == "true" {
        LOG.get_or_init(|| {
            tracing::warn!("disable comm subplan elim");
        });
        flags &= !OptFlags::COMM_SUBPLAN_ELIM;
    }
    flags
}
