use std::{any::Any, fmt::<PERSON>atter, marker::PhantomData, ops::Deref, sync::Arc};

use polars::prelude::AnonymousScan;

#[derive(Clone)]
pub struct ScanRef<T> {
    inner: Arc<dyn AnonymousScan>,
    _marker: PhantomData<T>,
}

impl<T> std::fmt::Debug for ScanRef<T> {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", Arc::as_ptr(&self.inner) as *const () as usize)
    }
}

impl<T> std::hash::Hash for ScanRef<T> {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        let addr = Arc::as_ptr(&self.inner) as *const () as usize;

        state.write_usize(addr);
    }
}

impl<T> PartialEq for ScanRef<T> {
    fn eq(&self, other: &Self) -> bool {
        std::ptr::addr_eq(Arc::as_ptr(&self.inner), Arc::as_ptr(&other.inner))
    }
}

impl<T> Eq for ScanRef<T> {}

impl<T: Any> Deref for ScanRef<T> {
    type Target = T;

    fn deref(&self) -> &Self::Target {
        //TODO: downcast_ref_unchecked in Any is unstable
        unsafe fn downcast_ref_unchecked<T: Any>(t: &dyn Any) -> &T {
            debug_assert!(t.is::<T>());
            // SAFETY: caller guarantees that T is the correct type
            unsafe { &*(t as *const dyn Any as *const T) }
        }

        unsafe { downcast_ref_unchecked(self.inner.as_any()) }
    }
}

impl<T: Any> ScanRef<T> {
    pub fn new(inner: &Arc<dyn AnonymousScan>) -> Option<Self> {
        if inner.as_any().is::<T>() {
            Some(Self {
                inner: inner.clone(),
                _marker: PhantomData,
            })
        } else {
            None
        }
    }
}
