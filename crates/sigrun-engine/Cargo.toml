[package]
name = "sigrun-engine"
edition = "2024"
version.workspace = true

[dependencies]
ahash = "0.8"
anyhow = { version = "1", features = ["backtrace"] }
serde_json = "1"
chrono = { version = "0.4.19" }
tracing = "0.1.40"
tokio = { version = "1.32.0", features = [
    "rt",
    "rt-multi-thread",
    "time",
    "sync",
] }
itertools = "0.14"
rayon = "1"
indexmap = { version = "2" }
smallvec = { version = "1" }
futures-core = "0.3"
futures-util = { version = "0.3", default-features = false }
async-broadcast = "0.7"
tokio-stream = "0.1"
fake = "4"
bon = "3"
pin-project = "1"
thiserror = "2"
paste = "1"

runtime = { path = "../runtime" }
sigrun-utils = { path = "../sigrun-utils" }
sigrun-io-expr = { path = "../sigrun-io-expr" }
sigrun-parser = { path = "../sigrun-parser" }


polars-convert = { git = "https://gitlab.sci-inv.cn/rustlib/polars-convert.git" }

sigrun-loader.workspace = true
virgo = { workspace = true, features = ["streaming", "mock", "table"] }
polars = { workspace = true, features = ["lazy", "serde"] }
polars-core = { workspace = true }
polars-time = { workspace = true }
polars-plan = { workspace = true }
polars-mem-engine = { workspace = true }
polars-utils = { workspace = true }
polars-expr = { workspace = true }
polars-python.workspace = true
pyo3.workspace = true
batch-parquet-reader = { workspace = true }


[dev-dependencies]
tokio = { version = "1", features = ["test-util"] }
